<resources>
    <string name="app_name">synapse</string>


    <!-- API设置相关字符串 -->
    <string name="settings_api_url_hint">例如: https://your-api-server.com/v1/chat/completions</string>
    <string name="settings_api_model_id_label">AI Model ID</string>

    <!-- 权限相关字符串 -->
    <string name="permission_title">权限检测</string>
    <string name="permission_overview_all_granted">所有权限已授予</string>
    <string name="permission_overview_need_grant">需要授予权限</string>
    <string name="permission_overview_status">已授予 %1$d/%2$d 项权限</string>
    <string name="permission_details_title">权限详情</string>
    <string name="permission_refresh_status">刷新状态</string>
    <string name="permission_request_all">请求权限</string>
    <string name="permission_request_this">请求此权限</string>
    <string name="permission_status_granted">已授予</string>
    <string name="permission_status_not_granted">未授予</string>

    <!-- 具体权限名称和描述 -->
    <string name="permission_notification_name">通知权限</string>
    <string name="permission_notification_desc">用于显示截图处理结果和状态通知</string>
    <string name="permission_media_name">相册读取权限</string>
    <string name="permission_media_desc">用于读取和处理截图文件</string>
    <string name="permission_storage_name">文件读取权限</string>
    <string name="permission_storage_desc">用于访问设备存储中的文件</string>
</resources>