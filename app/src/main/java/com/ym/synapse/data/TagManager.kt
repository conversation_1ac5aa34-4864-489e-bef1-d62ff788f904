package com.ym.synapse.data

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ym.synapse.PREFS_NAME

/**
 * 标签管理器
 * 负责管理和优化Notion标签的使用
 */
class TagManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "TagManager"
        private const val KEY_TAGS_DATA = "tags_data"
        private const val SIMILARITY_THRESHOLD = 0.7 // 相似度阈值
        
        @Volatile
        private var INSTANCE: TagManager? = null
        
        fun getInstance(context: Context): TagManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TagManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    /**
     * 标签数据类
     */
    data class TagData(
        val tag: String,
        val count: Int = 1,
        val lastUsed: Long = System.currentTimeMillis(),
        val imageTypes: MutableSet<String> = mutableSetOf()
    )
    
    /**
     * 获取所有标签数据
     */
    private fun getAllTagsData(): MutableMap<String, TagData> {
        val json = sharedPreferences.getString(KEY_TAGS_DATA, null)
        return if (json != null) {
            try {
                val type = object : TypeToken<MutableMap<String, TagData>>() {}.type
                gson.fromJson(json, type) ?: mutableMapOf()
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing tags data", e)
                mutableMapOf()
            }
        } else {
            mutableMapOf()
        }
    }
    
    /**
     * 保存标签数据
     */
    private fun saveTagsData(tagsData: MutableMap<String, TagData>) {
        try {
            val json = gson.toJson(tagsData)
            sharedPreferences.edit().putString(KEY_TAGS_DATA, json).apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving tags data", e)
        }
    }
    
    /**
     * 获取所有已使用的标签（按使用频率排序）
     */
    fun getAllTags(): List<String> {
        return getAllTagsData().values
            .sortedWith(compareByDescending<TagData> { it.count }.thenByDescending { it.lastUsed })
            .map { it.tag }
    }
    
    /**
     * 获取特定图片类型的常用标签
     */
    fun getTagsForImageType(imageType: String, limit: Int = 10): List<String> {
        return getAllTagsData().values
            .filter { it.imageTypes.contains(imageType) }
            .sortedWith(compareByDescending<TagData> { it.count }.thenByDescending { it.lastUsed })
            .take(limit)
            .map { it.tag }
    }
    
    /**
     * 智能处理标签列表
     * 对于每个新标签，查找相似的已有标签，如果找到则使用已有标签
     */
    fun processTagsIntelligently(newTags: List<String>, imageType: String): List<String> {
        val existingTagsData = getAllTagsData()
        val processedTags = mutableListOf<String>()
        
        for (newTag in newTags) {
            val cleanNewTag = newTag.trim()
            if (cleanNewTag.isEmpty()) continue
            
            // 查找最相似的已有标签
            val similarTag = findMostSimilarTag(cleanNewTag, existingTagsData.keys)
            
            val finalTag = if (similarTag != null) {
                Log.d(TAG, "Using existing similar tag '$similarTag' for new tag '$cleanNewTag'")
                similarTag
            } else {
                Log.d(TAG, "Creating new tag: '$cleanNewTag'")
                cleanNewTag
            }
            
            if (!processedTags.contains(finalTag)) {
                processedTags.add(finalTag)
            }
        }
        
        // 更新标签使用统计
        updateTagsUsage(processedTags, imageType)
        
        return processedTags
    }
    
    /**
     * 查找最相似的标签
     */
    private fun findMostSimilarTag(newTag: String, existingTags: Set<String>): String? {
        var maxSimilarity = 0.0
        var mostSimilarTag: String? = null
        
        for (existingTag in existingTags) {
            val similarity = calculateSimilarity(newTag, existingTag)
            if (similarity > maxSimilarity && similarity >= SIMILARITY_THRESHOLD) {
                maxSimilarity = similarity
                mostSimilarTag = existingTag
            }
        }
        
        return mostSimilarTag
    }
    
    /**
     * 计算两个字符串的相似度
     * 使用编辑距离算法
     */
    private fun calculateSimilarity(str1: String, str2: String): Double {
        val s1 = str1.lowercase()
        val s2 = str2.lowercase()
        
        // 完全匹配
        if (s1 == s2) return 1.0
        
        // 包含关系
        if (s1.contains(s2) || s2.contains(s1)) {
            return 0.8
        }
        
        // 编辑距离相似度
        val editDistance = levenshteinDistance(s1, s2)
        val maxLength = maxOf(s1.length, s2.length)
        
        return if (maxLength == 0) 1.0 else 1.0 - (editDistance.toDouble() / maxLength)
    }
    
    /**
     * 计算编辑距离
     */
    private fun levenshteinDistance(str1: String, str2: String): Int {
        val dp = Array(str1.length + 1) { IntArray(str2.length + 1) }
        
        for (i in 0..str1.length) {
            dp[i][0] = i
        }
        
        for (j in 0..str2.length) {
            dp[0][j] = j
        }
        
        for (i in 1..str1.length) {
            for (j in 1..str2.length) {
                val cost = if (str1[i - 1] == str2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // 删除
                    dp[i][j - 1] + 1,      // 插入
                    dp[i - 1][j - 1] + cost // 替换
                )
            }
        }
        
        return dp[str1.length][str2.length]
    }
    
    /**
     * 更新标签使用统计
     */
    private fun updateTagsUsage(tags: List<String>, imageType: String) {
        val tagsData = getAllTagsData()
        
        for (tag in tags) {
            val existing = tagsData[tag]
            if (existing != null) {
                // 更新已有标签
                tagsData[tag] = existing.copy(
                    count = existing.count + 1,
                    lastUsed = System.currentTimeMillis(),
                    imageTypes = existing.imageTypes.apply { add(imageType) }
                )
            } else {
                // 创建新标签
                tagsData[tag] = TagData(
                    tag = tag,
                    count = 1,
                    lastUsed = System.currentTimeMillis(),
                    imageTypes = mutableSetOf(imageType)
                )
            }
        }
        
        saveTagsData(tagsData)
        Log.d(TAG, "Updated tags usage. Total tags: ${tagsData.size}")
    }
    
    /**
     * 获取标签统计信息
     */
    fun getTagsStatistics(): Map<String, Int> {
        return getAllTagsData().mapValues { it.value.count }
    }
    
    /**
     * 清理低频标签（可选功能）
     */
    fun cleanupLowFrequencyTags(minCount: Int = 2) {
        val tagsData = getAllTagsData()
        val filteredTags = tagsData.filterValues { it.count >= minCount }.toMutableMap()
        
        if (filteredTags.size != tagsData.size) {
            saveTagsData(filteredTags)
            Log.d(TAG, "Cleaned up tags. Removed ${tagsData.size - filteredTags.size} low-frequency tags")
        }
    }
    
    /**
     * 生成标签提示文本（用于AI prompt）
     */
    fun generateTagSuggestionText(imageType: String): String {
        val commonTags = getTagsForImageType(imageType, 15)
        val allTags = getAllTags().take(30)
        
        return buildString {
            if (commonTags.isNotEmpty()) {
                appendLine("常用的${imageType}类型标签：${commonTags.joinToString("、")}")
            }
            if (allTags.isNotEmpty()) {
                appendLine("所有可用标签：${allTags.joinToString("、")}")
            }
            appendLine("请优先从上述标签中选择合适的，如果没有合适的再创建新标签。")
        }
    }
}
