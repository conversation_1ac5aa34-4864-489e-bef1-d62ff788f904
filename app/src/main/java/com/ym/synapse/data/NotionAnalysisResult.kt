package com.ym.synapse.data

import android.content.Context
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.annotations.SerializedName

/**
 * Notion分析结果数据类
 * 用于解析AI返回的JSON格式结果
 */
data class NotionAnalysisResult(
    @SerializedName("title")
    val title: String,
    
    @SerializedName("summary")
    val summary: String,
    
    @SerializedName("action_items")
    val actionItems: List<String> = emptyList(),
    
    @SerializedName("tags")
    val tags: List<String> = emptyList()
) {
    companion object {
        private val gson = Gson()
        
        /**
         * 从AI返回的文本中解析JSON结果
         * 支持处理包含markdown代码块的响应
         */
        fun parseFromAiResponse(response: String, context: Context? = null, imageType: String? = null): NotionAnalysisResult? {
            return try {
                // 尝试直接解析JSON
                val result = gson.fromJson(response.trim(), NotionAnalysisResult::class.java)
                processTagsIntelligently(result, context, imageType)
            } catch (e: JsonSyntaxException) {
                // 如果直接解析失败，尝试提取JSON代码块
                try {
                    val jsonContent = extractJsonFromMarkdown(response)
                    if (jsonContent != null) {
                        val result = gson.fromJson(jsonContent, NotionAnalysisResult::class.java)
                        processTagsIntelligently(result, context, imageType)
                    } else {
                        null
                    }
                } catch (e2: JsonSyntaxException) {
                    null
                }
            }
        }
        
        /**
         * 智能处理标签
         */
        private fun processTagsIntelligently(result: NotionAnalysisResult?, context: Context?, imageType: String?): NotionAnalysisResult? {
            if (result == null || context == null || imageType == null) {
                return result
            }

            val tagManager = TagManager.getInstance(context)
            val processedTags = tagManager.processTagsIntelligently(result.tags, imageType)

            return result.copy(tags = processedTags)
        }

        /**
         * 从markdown格式的响应中提取JSON内容
         */
        private fun extractJsonFromMarkdown(response: String): String? {
            // 查找 ```json 和 ``` 之间的内容
            val jsonBlockRegex = "```json\\s*\\n([\\s\\S]*?)\\n```".toRegex()
            val matchResult = jsonBlockRegex.find(response)
            return matchResult?.groupValues?.get(1)?.trim()
        }
        
        /**
         * 创建一个备用结果，当JSON解析失败时使用
         */
        fun createFallbackResult(originalResponse: String, imageType: String, context: Context? = null): NotionAnalysisResult {
            // 尝试从原始响应中提取标题（取前15个字符）
            val title = when {
                originalResponse.length <= 15 -> originalResponse
                else -> originalResponse.take(12) + "..."
            }
            
            // 根据图片类型生成默认标签
            val defaultTags = when (imageType) {
                "Text-Heavy" -> listOf("文本内容", "OCR识别")
                "Rich-Content" -> listOf("富内容", "界面截图")
                "Simple-Image" -> listOf("图片", "简单内容")
                else -> listOf("未分类")
            }

            // 智能处理标签
            val processedTags = if (context != null) {
                val tagManager = TagManager.getInstance(context)
                tagManager.processTagsIntelligently(defaultTags, imageType)
            } else {
                defaultTags
            }

            return NotionAnalysisResult(
                title = title,
                summary = originalResponse,
                actionItems = emptyList(),
                tags = processedTags
            )
        }
    }
    
    /**
     * 验证结果是否有效
     */
    fun isValid(): Boolean {
        return title.isNotBlank() && summary.isNotBlank()
    }
    
    /**
     * 获取格式化的标签字符串
     */
    fun getFormattedTags(): String {
        return tags.joinToString(", ")
    }
    
    /**
     * 获取格式化的行动项字符串
     */
    fun getFormattedActionItems(): String {
        return if (actionItems.isEmpty()) {
            "无"
        } else {
            actionItems.mapIndexed { index, item -> "${index + 1}. $item" }.joinToString("\n")
        }
    }
    
    /**
     * 转换为显示用的格式化文本
     */
    fun toDisplayText(): String {
        return buildString {
            appendLine("📋 **${title}**")
            appendLine()
            appendLine("📝 **内容摘要：**")
            appendLine(summary)
            
            if (actionItems.isNotEmpty()) {
                appendLine()
                appendLine("✅ **行动项：**")
                actionItems.forEachIndexed { index, item ->
                    appendLine("${index + 1}. $item")
                }
            }
            
            if (tags.isNotEmpty()) {
                appendLine()
                appendLine("🏷️ **标签：** ${getFormattedTags()}")
            }
        }
    }
}
