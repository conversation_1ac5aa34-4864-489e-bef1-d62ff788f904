package com.ym.synapse.api

import com.google.gson.annotations.SerializedName
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.*

/**
 * ImgBB API接口
 */
interface ImgBBApiService {
    @Multipart
    @POST("1/upload")
    suspend fun uploadImage(
        @Query("key") apiKey: String,
        @Part image: MultipartBody.Part,
        @Part("name") name: okhttp3.RequestBody? = null
    ): Response<ImgBBResponse>
}

/**
 * SM.MS API接口
 */
interface SmMsApiService {
    @Multipart
    @POST("v2/upload")
    suspend fun uploadImage(
        @Header("Authorization") authorization: String,
        @Part smfile: MultipartBody.Part
    ): Response<SmMsResponse>
}

/**
 * ImgBB响应数据
 */
data class ImgBBResponse(
    @SerializedName("data")
    val data: ImgBBData?,
    
    @SerializedName("success")
    val success: <PERSON><PERSON><PERSON>,
    
    @SerializedName("status")
    val status: Int
)

data class ImgBBData(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("url_viewer")
    val urlViewer: String,
    
    @SerializedName("url")
    val url: String,
    
    @SerializedName("display_url")
    val displayUrl: String,
    
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int,
    
    @SerializedName("size")
    val size: Int,
    
    @SerializedName("time")
    val time: String,
    
    @SerializedName("expiration")
    val expiration: String,
    
    @SerializedName("image")
    val image: ImgBBImageInfo,
    
    @SerializedName("thumb")
    val thumb: ImgBBImageInfo,
    
    @SerializedName("medium")
    val medium: ImgBBImageInfo? = null,
    
    @SerializedName("delete_url")
    val deleteUrl: String
)

data class ImgBBImageInfo(
    @SerializedName("filename")
    val filename: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("mime")
    val mime: String,
    
    @SerializedName("extension")
    val extension: String,
    
    @SerializedName("url")
    val url: String
)

/**
 * SM.MS响应数据
 */
data class SmMsResponse(
    @SerializedName("success")
    val success: Boolean,
    
    @SerializedName("code")
    val code: String,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: SmMsData?,
    
    @SerializedName("RequestId")
    val requestId: String
)

data class SmMsData(
    @SerializedName("file_id")
    val fileId: String,
    
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int,
    
    @SerializedName("filename")
    val filename: String,
    
    @SerializedName("storename")
    val storename: String,
    
    @SerializedName("size")
    val size: Int,
    
    @SerializedName("path")
    val path: String,
    
    @SerializedName("hash")
    val hash: String,
    
    @SerializedName("url")
    val url: String,
    
    @SerializedName("delete")
    val delete: String,
    
    @SerializedName("page")
    val page: String
)

/**
 * 图床上传结果
 */
sealed class ImageHostResult {
    data class Success(val url: String, val deleteUrl: String? = null) : ImageHostResult()
    data class Error(val message: String) : ImageHostResult()
}

/**
 * 图床服务配置
 */
data class ImageHostConfig(
    val service: String,
    val apiKey: String,
    val enabled: Boolean
) {
    fun isValid(): Boolean {
        return enabled && apiKey.isNotBlank() && service.isNotBlank()
    }
}
