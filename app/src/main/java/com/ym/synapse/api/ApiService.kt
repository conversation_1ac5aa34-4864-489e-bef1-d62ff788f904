package com.ym.synapse.api

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.util.concurrent.TimeUnit

/**
 * API服务类，用于与OpenAI格式的API进行交互
 * 修复：使用单例模式复用OkHttpClient，避免资源浪费
 */
class ApiService {

    companion object {
        // 使用单例模式复用OkHttpClient实例，避免重复创建
        @Volatile
        private var INSTANCE: OkHttpClient? = null

        private fun getHttpClient(): OkHttpClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS) // 增加读取超时，AI请求可能较慢
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true) // 启用连接失败重试
                    .build().also { INSTANCE = it }
            }
        }
    }

    private val client = getHttpClient()

    /**
     * 模型信息数据类
     */
    data class ModelInfo(
        val id: String,
        val name: String,
        val description: String = ""
    )

    /**
     * API响应结果
     */
    sealed class ApiResult<T> {
        data class Success<T>(val data: T) : ApiResult<T>()
        data class Error<T>(val message: String, val exception: Throwable? = null) : ApiResult<T>()
    }

    /**
     * 获取可用模型列表
     */
    suspend fun getModels(apiUrl: String, apiKey: String): ApiResult<List<ModelInfo>> {
        return withContext(Dispatchers.IO) {
            try {
                // 构建模型列表API URL
                val baseUrl = when {
                    apiUrl.endsWith("/chat/completions") -> apiUrl.replace("/chat/completions", "")
                    apiUrl.endsWith("/") -> apiUrl.trimEnd('/')
                    else -> apiUrl
                }
                val modelsUrl = "$baseUrl/models"

                val request = Request.Builder()
                    .url(modelsUrl)
                    .addHeader("Authorization", "Bearer $apiKey")
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .build()

                // 使用use确保响应资源被正确释放
                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        if (responseBody != null) {
                            val jsonObject = JSONObject(responseBody)
                            val dataArray = jsonObject.getJSONArray("data")

                            val models = mutableListOf<ModelInfo>()
                            for (i in 0 until dataArray.length()) {
                                val modelObject = dataArray.getJSONObject(i)
                                val id = modelObject.getString("id")

                                // 过滤掉一些不常用的模型
                                if (isValidModel(id)) {
                                    models.add(
                                        ModelInfo(
                                            id = id,
                                            name = formatModelName(id),
                                            description = getModelDescription(id)
                                        )
                                    )
                                }
                            }

                            // 按名称排序
                            models.sortBy { it.name }
                            ApiResult.Success(models)
                        } else {
                            ApiResult.Error("响应体为空")
                        }
                    } else {
                        ApiResult.Error("HTTP ${response.code}: ${response.message}")
                    }
                }
            } catch (e: java.net.SocketTimeoutException) {
                ApiResult.Error("请求超时，请检查网络连接", e)
            } catch (e: java.net.UnknownHostException) {
                ApiResult.Error("无法连接到服务器，请检查API URL", e)
            } catch (e: java.io.IOException) {
                ApiResult.Error("网络连接错误: ${e.message}", e)
            } catch (e: Exception) {
                ApiResult.Error("网络请求失败: ${e.message}", e)
            }
        }
    }

    /**
     * 测试API连接
     */
    suspend fun testConnection(apiUrl: String, apiKey: String): ApiResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                val baseUrl = when {
                    apiUrl.endsWith("/chat/completions") -> apiUrl.replace("/chat/completions", "")
                    apiUrl.endsWith("/") -> apiUrl.trimEnd('/')
                    else -> apiUrl
                }
                val modelsUrl = "$baseUrl/models"

                val request = Request.Builder()
                    .url(modelsUrl)
                    .addHeader("Authorization", "Bearer $apiKey")
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .build()

                val response = client.newCall(request).execute()
                
                if (response.isSuccessful) {
                    ApiResult.Success("连接成功")
                } else {
                    ApiResult.Error("连接失败: HTTP ${response.code}")
                }
            } catch (e: Exception) {
                ApiResult.Error("连接失败: ${e.message}", e)
            }
        }
    }

    /**
     * 判断是否为有效的模型
     */
    private fun isValidModel(modelId: String): Boolean {
        val invalidPrefixes = listOf(
            "whisper-", "tts-", "dall-e", "text-embedding", "text-moderation"
        )
        return !invalidPrefixes.any { modelId.startsWith(it) }
    }

    /**
     * 格式化模型名称
     */
    private fun formatModelName(modelId: String): String {
        return when {
            modelId.startsWith("gpt-4o") -> "GPT-4o ${modelId.removePrefix("gpt-4o").takeIf { it.isNotEmpty() } ?: ""}"
            modelId.startsWith("gpt-4") -> "GPT-4 ${modelId.removePrefix("gpt-4").takeIf { it.isNotEmpty() } ?: ""}"
            modelId.startsWith("gpt-3.5") -> "GPT-3.5 ${modelId.removePrefix("gpt-3.5").takeIf { it.isNotEmpty() } ?: ""}"
            modelId.startsWith("claude") -> "Claude ${modelId.removePrefix("claude").takeIf { it.isNotEmpty() } ?: ""}"
            else -> modelId.uppercase()
        }.trim()
    }

    /**
     * 获取模型描述
     */
    private fun getModelDescription(modelId: String): String {
        return when {
            modelId.contains("gpt-4o") -> "最新的GPT-4o模型，性能优异"
            modelId.contains("gpt-4") -> "GPT-4系列模型，功能强大"
            modelId.contains("gpt-3.5") -> "GPT-3.5系列模型，性价比高"
            modelId.contains("claude") -> "Anthropic Claude模型"
            else -> "AI语言模型"
        }
    }
}
