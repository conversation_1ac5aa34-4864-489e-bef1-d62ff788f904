package com.ym.synapse.api

import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Url

/**
 * AI OCR API接口
 */
interface AiOcrApiService {
    @POST
    suspend fun recognizeImage(
        @Url fullUrl: String,
        @Header("Authorization") authorization: String,
        @Body request: AiOcrRequest
    ): AiOcrResponse
}

/**
 * AI OCR请求数据类
 */
data class AiOcrRequest(
    val model: String,
    val messages: List<Message>,
    val max_tokens: Int = 1000,
    val temperature: Double = 0.7
)

/**
 * 消息数据类
 */
data class Message(
    val role: String,
    val content: List<ContentPart>
)

/**
 * 内容部分数据类
 */
data class ContentPart(
    val type: String,
    val text: String? = null,
    val image_url: ImageUrl? = null
)

/**
 * 图片URL数据类
 */
data class ImageUrl(
    val url: String
)

/**
 * AI OCR响应数据类
 */
data class AiOcrResponse(
    val id: String? = null,
    val object_type: String? = null,
    val created: Long? = null,
    val model: String? = null,
    val choices: List<Choice>? = null,
    val usage: Usage? = null
)

/**
 * 选择数据类
 */
data class Choice(
    val index: Int? = null,
    val message: ResponseMessage? = null,
    val finish_reason: String? = null
)

/**
 * 响应消息数据类
 */
data class ResponseMessage(
    val role: String? = null,
    val content: String? = null
)

/**
 * 使用情况数据类
 */
data class Usage(
    val prompt_tokens: Int? = null,
    val completion_tokens: Int? = null,
    val total_tokens: Int? = null
)
