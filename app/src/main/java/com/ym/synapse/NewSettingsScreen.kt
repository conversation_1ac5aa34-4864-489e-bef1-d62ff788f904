package com.ym.synapse

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.activity.compose.BackHandler
import com.ym.synapse.ui.animations.*
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 重构后的设置界面 - 主入口
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewSettingsScreen() {
    var currentScreen by remember { mutableStateOf("main") }

    // 处理系统返回键
    BackHandler(enabled = currentScreen != "main") {
        currentScreen = "main"
    }

    AnimatedScreenTransition(
        targetState = currentScreen,
        modifier = Modifier.fillMaxSize()
    ) { screen ->
        when (screen) {
            "main" -> MainSettingsScreen(
                onNavigate = { newScreen -> currentScreen = newScreen }
            )
            "api_config" -> com.ym.synapse.config.ApiConfigDetailScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
            "model_config" -> com.ym.synapse.config.ModelConfigDetailScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
            "notion_config" -> com.ym.synapse.config.NotionConfigDetailScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
            "image_host_config" -> com.ym.synapse.config.ImageHostConfigDetailScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
            "tag_management" -> TagManagementScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
            "about" -> AboutScreen(
                onBackClick = {
                    currentScreen = "main"
                }
            )
        }
    }
}

/**
 * 主设置界面 - 功能分类菜单
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainSettingsScreen(
    onNavigate: (String) -> Unit
) {
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设置") }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // API配置分类
            item {
                SettingsCategoryHeader("API配置")
            }

            itemsIndexed(getApiConfigItems()) { index, item ->
                AnimatedListItem(
                    visible = true,
                    index = index
                ) {
                    SettingsItem(
                        title = item.title,
                        subtitle = item.subtitle,
                        icon = item.icon,
                        onClick = { onNavigate(item.route) }
                    )
                }
            }
            
            // 模型配置分类
            item {
                SettingsCategoryHeader("模型配置")
            }

            itemsIndexed(getModelConfigItems()) { index, item ->
                AnimatedListItem(
                    visible = true,
                    index = index + 1 // 偏移索引以创建错开效果
                ) {
                    SettingsItem(
                        title = item.title,
                        subtitle = item.subtitle,
                        icon = item.icon,
                        onClick = { onNavigate(item.route) }
                    )
                }
            }

            // 集成服务分类
            item {
                SettingsCategoryHeader("集成服务")
            }

            itemsIndexed(getIntegrationItems()) { index, item ->
                AnimatedListItem(
                    visible = true,
                    index = index + 2
                ) {
                    SettingsItem(
                        title = item.title,
                        subtitle = item.subtitle,
                        icon = item.icon,
                        onClick = { onNavigate(item.route) }
                    )
                }
            }

            // 数据管理分类
            item {
                SettingsCategoryHeader("数据管理")
            }

            itemsIndexed(getDataManagementItems()) { index, item ->
                AnimatedListItem(
                    visible = true,
                    index = index + 4
                ) {
                    SettingsItem(
                        title = item.title,
                        subtitle = item.subtitle,
                        icon = item.icon,
                        onClick = { onNavigate(item.route) }
                    )
                }
            }

            // 关于分类
            item {
                SettingsCategoryHeader("关于")
            }

            itemsIndexed(getAboutItems()) { index, item ->
                AnimatedListItem(
                    visible = true,
                    index = index + 5
                ) {
                    SettingsItem(
                        title = item.title,
                        subtitle = item.subtitle,
                        icon = item.icon,
                        onClick = { onNavigate(item.route) }
                    )
                }
            }
        }
    }
}

/**
 * 设置分类标题
 */
@Composable
fun SettingsCategoryHeader(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

/**
 * 设置项组件
 */
@Composable
fun SettingsItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .bounceAnimation(triggered = isPressed),
        onClick = {
            isPressed = true
            onClick()
            // 重置动画状态
            GlobalScope.launch {
                delay(150)
                isPressed = false
            }
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 设置项数据类
 */
data class SettingsItemData(
    val title: String,
    val subtitle: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val route: String
)

/**
 * 获取API配置项
 */
fun getApiConfigItems(): List<SettingsItemData> {
    return listOf(
        SettingsItemData(
            title = "基础API配置",
            subtitle = "配置AI服务的API地址和密钥",
            icon = Icons.Default.Api,
            route = "api_config"
        )
    )
}

/**
 * 获取模型配置项
 */
fun getModelConfigItems(): List<SettingsItemData> {
    return listOf(
        SettingsItemData(
            title = "模型配置",
            subtitle = "配置不同图片类型的AI模型和提示词",
            icon = Icons.Default.Psychology,
            route = "model_config"
        )
    )
}

/**
 * 获取集成服务项
 */
fun getIntegrationItems(): List<SettingsItemData> {
    return listOf(
        SettingsItemData(
            title = "Notion集成",
            subtitle = "配置Notion数据库连接和自动同步",
            icon = Icons.Default.CloudSync,
            route = "notion_config"
        ),
        SettingsItemData(
            title = "图床服务",
            subtitle = "配置图片上传服务，在Notion中显示图片",
            icon = Icons.Default.Image,
            route = "image_host_config"
        )
    )
}

/**
 * 获取数据管理项
 */
fun getDataManagementItems(): List<SettingsItemData> {
    return listOf(
        SettingsItemData(
            title = "标签管理",
            subtitle = "查看和管理AI生成的标签，保持标签一致性",
            icon = Icons.Default.Label,
            route = "tag_management"
        )
    )
}

/**
 * 获取关于项
 */
fun getAboutItems(): List<SettingsItemData> {
    return listOf(
        SettingsItemData(
            title = "关于应用",
            subtitle = "版本信息、使用指南和技术支持",
            icon = Icons.Default.Info,
            route = "about"
        )
    )
}

/**
 * 关于界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(
    onBackClick: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("关于") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(32.dp))
            
            Icon(
                imageVector = Icons.Default.CameraAlt,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Synapse",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = "智能截图分析助手",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "功能特性",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    val features = listOf(
                        "🤖 AI智能图片分析",
                        "📝 三种分析模式：文字密集、富内容、简单图片",
                        "🏷️ 智能标签管理系统",
                        "📚 Notion知识库集成",
                        "🖼️ 图床服务支持",
                        "⚡ 双击电源键快速截图"
                    )
                    
                    features.forEach { feature ->
                        Text(
                            text = feature,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
    }
}
