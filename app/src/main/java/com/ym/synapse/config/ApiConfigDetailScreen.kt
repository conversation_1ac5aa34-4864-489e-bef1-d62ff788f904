package com.ym.synapse.config

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.*

/**
 * API配置详细界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiConfigDetailScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var apiUrl by remember { mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "") }
    var apiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "") }
    var modelId by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("API配置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "API配置说明",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "配置AI服务的基础连接信息。支持OpenAI兼容的API服务，如OpenAI、Claude、本地部署的模型等。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // API URL配置
            OutlinedTextField(
                value = apiUrl,
                onValueChange = { apiUrl = it },
                label = { Text("API URL") },
                placeholder = { Text("https://api.openai.com/v1/chat/completions") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("AI服务的API地址，支持OpenAI兼容接口")
                }
            )

            // API Key配置
            OutlinedTextField(
                value = apiKey,
                onValueChange = { apiKey = it },
                label = { Text("API Key") },
                placeholder = { Text("sk-...") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("API密钥，用于身份验证")
                }
            )

            // 模型ID配置
            OutlinedTextField(
                value = modelId,
                onValueChange = { modelId = it },
                label = { Text("模型ID") },
                placeholder = { Text("gpt-4-vision-preview") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("AI模型标识符，需要支持视觉分析功能")
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            // 保存按钮
            Button(
                onClick = {
                    with(sharedPreferences.edit()) {
                        putString(KEY_API_URL, apiUrl)
                        putString(KEY_API_KEY, apiKey)
                        putString(KEY_AI_OCR_MODEL_ID, modelId)
                        apply()
                    }
                    android.widget.Toast.makeText(context, "API配置已保存", android.widget.Toast.LENGTH_SHORT).show()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("保存配置")
            }

            // 重置按钮
            OutlinedButton(
                onClick = {
                    apiUrl = ""
                    apiKey = ""
                    modelId = DEFAULT_AI_OCR_MODEL_ID
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("重置为默认值")
            }
        }
    }
}
