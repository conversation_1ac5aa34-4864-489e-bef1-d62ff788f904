package com.ym.synapse

import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.ym.synapse.api.ApiService
import com.ym.synapse.ui.theme.SynapseTheme
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiConfigScreen(
    onBackClick: () -> Unit = {},
    onConfigComplete: () -> Unit = {}
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    // API配置状态
    var apiUrl by remember {
        mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "")
    }
    var apiKey by remember {
        mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "")
    }
    var modelId by remember {
        mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID)
    }

    // 自动保存配置
    LaunchedEffect(apiUrl, apiKey, modelId) {
        if (apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()) {
            // 自动补充完整的API URL
            val fullApiUrl = if (apiUrl.endsWith("/chat/completions")) {
                apiUrl
            } else if (apiUrl.endsWith("/")) {
                "${apiUrl}chat/completions"
            } else {
                "$apiUrl/chat/completions"
            }

            // 自动保存配置
            with(sharedPreferences.edit()) {
                putString(KEY_API_URL, fullApiUrl)
                putString(KEY_API_KEY, apiKey)
                putString(KEY_AI_OCR_MODEL_ID, modelId)
                apply()
            }
        }
    }

    var showApiKey by remember { mutableStateOf(false) }
    var isLoadingModels by remember { mutableStateOf(false) }
    var availableModels by remember { mutableStateOf<List<ApiService.ModelInfo>>(emptyList()) }
    var showModelBottomSheet by remember { mutableStateOf(false) }
    var searchQuery by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    val apiService = remember { ApiService() }
    val coroutineScope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 顶部栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "API配置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 配置说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "配置OpenAI格式API",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "请配置兼容OpenAI格式的API服务，用于AI OCR功能",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // API URL配置
        OutlinedTextField(
            value = apiUrl,
            onValueChange = { apiUrl = it },
            label = { Text("API Base URL") },
            placeholder = { Text("https://api.openai.com/v1") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            supportingText = {
                Text(
                    text = "只需输入基础URL，程序会自动补充 /chat/completions",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // API Key配置
        OutlinedTextField(
            value = apiKey,
            onValueChange = { apiKey = it },
            label = { Text("API Key") },
            placeholder = { Text("your-api-key") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            visualTransformation = if (showApiKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showApiKey = !showApiKey }) {
                    Icon(
                        if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showApiKey) "隐藏" else "显示"
                    )
                }
            },
            supportingText = {
                Text(
                    text = "输入您的API密钥，无需添加sk-前缀",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 模型ID配置
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Bottom
        ) {
            OutlinedTextField(
                value = modelId,
                onValueChange = { modelId = it },
                label = { Text("模型ID") },
                placeholder = { Text("gpt-4o-mini") },
                modifier = Modifier.weight(1f),
                singleLine = true,
                trailingIcon = if (availableModels.isNotEmpty()) {
                    {
                        IconButton(onClick = { showModelBottomSheet = true }) {
                            Icon(Icons.Default.ArrowDropDown, contentDescription = "选择模型")
                        }
                    }
                } else null
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 获取模型列表按钮
            OutlinedButton(
                onClick = {
                    if (apiUrl.isNotBlank() && apiKey.isNotBlank()) {
                        isLoadingModels = true
                        errorMessage = null
                        coroutineScope.launch {
                            when (val result = apiService.getModels(apiUrl, apiKey)) {
                                is ApiService.ApiResult.Success -> {
                                    availableModels = result.data
                                    isLoadingModels = false
                                    if (result.data.isNotEmpty()) {
                                        showModelBottomSheet = true
                                    }
                                }
                                is ApiService.ApiResult.Error -> {
                                    errorMessage = result.message
                                    isLoadingModels = false
                                }
                            }
                        }
                    } else {
                        errorMessage = "请先填写API URL和API Key"
                    }
                },
                enabled = !isLoadingModels && apiUrl.isNotBlank() && apiKey.isNotBlank()
            ) {
                if (isLoadingModels) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(Icons.Default.Refresh, contentDescription = "获取模型")
                }
            }
        }

        // 模型选择底部抽屉
        if (showModelBottomSheet && availableModels.isNotEmpty()) {
            ModalBottomSheet(
                onDismissRequest = {
                    showModelBottomSheet = false
                    searchQuery = "" // 关闭时清空搜索
                }
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "选择模型",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // 过滤模型列表
                    val filteredModels = remember(availableModels, searchQuery) {
                        if (searchQuery.isBlank()) {
                            availableModels
                        } else {
                            availableModels.filter { model ->
                                model.name.contains(searchQuery, ignoreCase = true) ||
                                model.id.contains(searchQuery, ignoreCase = true) ||
                                model.description.contains(searchQuery, ignoreCase = true)
                            }
                        }
                    }

                    if (filteredModels.isEmpty()) {
                        // 无搜索结果
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "未找到匹配的模型",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        LazyColumn(
                            modifier = Modifier.heightIn(max = 400.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(filteredModels) { model ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        modelId = model.id
                                        showModelBottomSheet = false
                                    },
                                colors = CardDefaults.cardColors(
                                    containerColor = if (model.id == modelId)
                                        MaterialTheme.colorScheme.primaryContainer
                                    else
                                        MaterialTheme.colorScheme.surface
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = model.name,
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Medium,
                                        color = if (model.id == modelId)
                                            MaterialTheme.colorScheme.onPrimaryContainer
                                        else
                                            MaterialTheme.colorScheme.onSurface
                                    )
                                    if (model.description.isNotEmpty()) {
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Text(
                                            text = model.description,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = if (model.id == modelId)
                                                MaterialTheme.colorScheme.onPrimaryContainer
                                            else
                                                MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "ID: ${model.id}",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = if (model.id == modelId)
                                            MaterialTheme.colorScheme.onPrimaryContainer
                                        else
                                            MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                    }

                    // 搜索栏放在底部
                    OutlinedTextField(
                        value = searchQuery,
                        onValueChange = { searchQuery = it },
                        label = { Text("搜索模型") },
                        placeholder = { Text("输入模型名称或ID") },
                        leadingIcon = {
                            Icon(Icons.Default.Search, contentDescription = "搜索")
                        },
                        trailingIcon = if (searchQuery.isNotEmpty()) {
                            {
                                IconButton(onClick = { searchQuery = "" }) {
                                    Icon(Icons.Default.Clear, contentDescription = "清空")
                                }
                            }
                        } else null,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp),
                        singleLine = true
                    )

                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }

        // 错误消息
        if (errorMessage != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = errorMessage!!,
                    modifier = Modifier.padding(12.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))

        // 使用教程按钮
        if (apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()) {
            Button(
                onClick = onConfigComplete,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Text("使用教程")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ApiConfigScreenPreview() {
    SynapseTheme {
        ApiConfigScreen()
    }
}
