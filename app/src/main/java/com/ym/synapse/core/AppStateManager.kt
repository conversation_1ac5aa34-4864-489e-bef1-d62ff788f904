package com.ym.synapse.core

import android.content.Context
import com.ym.synapse.data.repository.ConfigRepository
import com.ym.synapse.utils.PermissionHelper
import com.ym.synapse.utils.RootLspHelper
import com.ym.synapse.utils.ResourceManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * 应用状态管理器
 * 统一管理应用的全局状态，包括权限、配置、初始化状态等
 */
class AppStateManager private constructor(
    private val context: Context
) {
    
    companion object {
        @Volatile
        private var INSTANCE: AppStateManager? = null
        
        fun getInstance(context: Context): AppStateManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AppStateManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val configRepository = ConfigRepository.getInstance(context)
    
    // 应用状态流
    private val _appState = MutableStateFlow(AppState())
    val appState: StateFlow<AppState> = _appState.asStateFlow()
    
    // 权限状态流
    private val _permissionState = MutableStateFlow(PermissionState())
    val permissionState: StateFlow<PermissionState> = _permissionState.asStateFlow()
    
    // 初始化状态流
    private val _initializationState = MutableStateFlow(InitializationState())
    val initializationState: StateFlow<InitializationState> = _initializationState.asStateFlow()
    
    init {
        // 监听配置变化并更新应用状态
        scope.launch {
            combine(
                configRepository.apiConfig,
                configRepository.appState
            ) { apiConfig, appState ->
                _appState.value = _appState.value.copy(
                    isApiConfigured = apiConfig.isConfigured,
                    showApiConfig = appState.showApiConfig,
                    showTutorial = appState.showTutorial,
                    hasCompletedSetup = appState.hasCompletedSetup
                )
            }
        }
        
        // 初始化应用
        initializeApp()
    }
    
    /**
     * 初始化应用
     */
    private fun initializeApp() {
        scope.launch {
            _initializationState.value = _initializationState.value.copy(
                isInitializing = true,
                currentStep = "检查权限..."
            )
            
            // 检查权限
            checkPermissions()
            
            _initializationState.value = _initializationState.value.copy(
                currentStep = "检查配置..."
            )
            
            // 检查配置
            checkConfiguration()
            
            _initializationState.value = _initializationState.value.copy(
                isInitializing = false,
                isCompleted = true,
                currentStep = "初始化完成"
            )
        }
    }
    
    /**
     * 检查权限状态
     */
    fun checkPermissions() {
        scope.launch {
            try {
                // 检查基本权限
                val basicPermissions = PermissionHelper.getAllPermissions(context)
                val allBasicGranted = PermissionHelper.areAllRequiredPermissionsGranted(context)
                
                // 检查Root和LSP权限
                val rootLspStatus = RootLspHelper.checkAllPermissions(context)
                
                _permissionState.value = PermissionState(
                    basicPermissions = basicPermissions,
                    allBasicPermissionsGranted = allBasicGranted,
                    rootLspStatus = rootLspStatus,
                    allPermissionsGranted = allBasicGranted && rootLspStatus.hasRoot && 
                                         rootLspStatus.hasLspFramework && rootLspStatus.hasLspModule,
                    lastChecked = System.currentTimeMillis()
                )
            } catch (e: Exception) {
                _permissionState.value = _permissionState.value.copy(
                    hasError = true,
                    errorMessage = "权限检查失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 检查配置状态
     */
    private fun checkConfiguration() {
        val shouldShowSetup = configRepository.shouldShowInitialSetup()
        _appState.value = _appState.value.copy(
            shouldShowInitialSetup = shouldShowSetup
        )
    }
    
    /**
     * 更新API配置状态
     */
    fun updateApiConfigState(show: Boolean) {
        configRepository.updateShowApiConfig(show)
    }
    
    /**
     * 更新教程状态
     */
    fun updateTutorialState(show: Boolean) {
        configRepository.updateShowTutorial(show)
    }
    
    /**
     * 完成设置
     */
    fun completeSetup() {
        configRepository.markSetupCompleted()
        _appState.value = _appState.value.copy(
            hasCompletedSetup = true,
            shouldShowInitialSetup = false
        )
    }
    
    /**
     * 重置应用状态
     */
    fun resetApp() {
        scope.launch {
            configRepository.resetAllConfigs()
            _appState.value = AppState()
            _permissionState.value = PermissionState()
            initializeApp()
        }
    }
    
    /**
     * 获取应用摘要信息
     */
    fun getAppSummary(): String {
        val appState = _appState.value
        val permissionState = _permissionState.value
        val initState = _initializationState.value
        
        return buildString {
            appendLine("=== 应用状态摘要 ===")
            appendLine("初始化状态: ${if (initState.isCompleted) "完成" else "进行中"}")
            appendLine("当前步骤: ${initState.currentStep}")
            appendLine("API配置: ${if (appState.isApiConfigured) "已配置" else "未配置"}")
            appendLine("设置完成: ${if (appState.hasCompletedSetup) "是" else "否"}")
            appendLine("基础权限: ${if (permissionState.allBasicPermissionsGranted) "已授予" else "未授予"}")
            appendLine("所有权限: ${if (permissionState.allPermissionsGranted) "已授予" else "未授予"}")
            appendLine("权限检查时间: ${java.text.SimpleDateFormat("HH:mm:ss").format(java.util.Date(permissionState.lastChecked))}")
            if (permissionState.hasError) {
                appendLine("错误信息: ${permissionState.errorMessage}")
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        ResourceManager.cleanupAllResources()
    }
}

/**
 * 应用状态数据类
 */
data class AppState(
    val isApiConfigured: Boolean = false,
    val showApiConfig: Boolean = false,
    val showTutorial: Boolean = false,
    val hasCompletedSetup: Boolean = false,
    val shouldShowInitialSetup: Boolean = true
)

/**
 * 权限状态数据类
 */
data class PermissionState(
    val basicPermissions: List<PermissionHelper.PermissionInfo> = emptyList(),
    val allBasicPermissionsGranted: Boolean = false,
    val rootLspStatus: RootLspHelper.PermissionStatus? = null,
    val allPermissionsGranted: Boolean = false,
    val lastChecked: Long = 0L,
    val hasError: Boolean = false,
    val errorMessage: String? = null
)

/**
 * 初始化状态数据类
 */
data class InitializationState(
    val isInitializing: Boolean = false,
    val isCompleted: Boolean = false,
    val currentStep: String = "",
    val progress: Float = 0f
)
