package com.ym.synapse.ui

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.ym.synapse.core.AppStateManager
import com.ym.synapse.ui.main.MainViewModel

/**
 * ViewModel工厂
 * 负责创建ViewModel实例并注入依赖
 */
class ViewModelFactory private constructor(
    private val context: Context
) : ViewModelProvider.Factory {
    
    companion object {
        @Volatile
        private var INSTANCE: ViewModelFactory? = null
        
        fun getInstance(context: Context): ViewModelFactory {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ViewModelFactory(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // 延迟初始化依赖
    private val appStateManager by lazy { AppStateManager.getInstance(context) }

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(MainViewModel::class.java) -> {
                MainViewModel(appStateManager) as T
            }
            // 在这里添加其他ViewModel
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}

/**
 * Compose中使用ViewModel的扩展函数
 */
@androidx.compose.runtime.Composable
inline fun <reified T : ViewModel> viewModel(
    context: Context = androidx.compose.ui.platform.LocalContext.current
): T {
    val factory = ViewModelFactory.getInstance(context)
    return androidx.lifecycle.viewmodel.compose.viewModel(factory = factory)
}
