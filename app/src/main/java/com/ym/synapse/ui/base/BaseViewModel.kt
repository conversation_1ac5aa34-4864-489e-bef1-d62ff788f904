package com.ym.synapse.ui.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ym.synapse.utils.ErrorHandler
import com.ym.synapse.utils.ResourceManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel基类
 * 提供统一的错误处理、加载状态管理和资源管理
 */
abstract class BaseViewModel : ViewModel() {
    
    // 通用UI状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 协程异常处理器
    protected val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        handleError(exception)
    }
    
    /**
     * 安全执行协程操作
     */
    protected fun launchSafely(
        showLoading: Boolean = true,
        block: suspend () -> Unit
    ) {
        viewModelScope.launch(exceptionHandler) {
            try {
                if (showLoading) _isLoading.value = true
                clearError()
                block()
            } finally {
                if (showLoading) _isLoading.value = false
            }
        }
    }
    
    /**
     * 处理错误
     */
    protected open fun handleError(exception: Throwable) {
        val errorMessage = when (exception) {
            is java.net.SocketTimeoutException -> "网络连接超时"
            is java.net.UnknownHostException -> "无法连接到服务器"
            is java.io.IOException -> "网络连接错误"
            is SecurityException -> "权限不足"
            else -> exception.message ?: "发生未知错误"
        }
        
        _error.value = errorMessage
        
        // 使用ErrorHandler记录详细错误
        ErrorHandler.handleError(
            context = null, // ViewModel中没有Context，由UI层处理用户提示
            errorInfo = ErrorHandler.ErrorInfo(
                type = when (exception) {
                    is java.net.SocketTimeoutException,
                    is java.net.UnknownHostException,
                    is java.io.IOException -> ErrorHandler.ErrorType.NETWORK
                    is SecurityException -> ErrorHandler.ErrorType.PERMISSION
                    else -> ErrorHandler.ErrorType.UNKNOWN
                },
                message = errorMessage,
                exception = exception,
                context = this::class.simpleName,
                showToUser = false // 由UI层决定是否显示给用户
            )
        )
    }
    
    /**
     * 清除错误状态
     */
    protected fun clearError() {
        _error.value = null
    }
    
    /**
     * 设置加载状态
     */
    protected fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
    
    /**
     * 注册资源到ResourceManager
     */
    protected fun registerResource(
        resourceId: String,
        cleanupAction: () -> Unit
    ) {
        ResourceManager.registerCustom(
            resourceId = "${this::class.simpleName}_$resourceId",
            cleanupAction = cleanupAction
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理ViewModel相关的资源
        val viewModelId = this::class.simpleName ?: "UnknownViewModel"
        ResourceManager.cleanupResource("${viewModelId}_*")
    }
}

/**
 * UI状态基类
 */
abstract class UiState {
    object Loading : UiState()
    object Success : UiState()
    data class Error(val message: String) : UiState()
}

/**
 * UI事件基类
 */
interface UiEvent

/**
 * UI效果基类（一次性事件，如导航、Toast等）
 */
interface UiEffect
