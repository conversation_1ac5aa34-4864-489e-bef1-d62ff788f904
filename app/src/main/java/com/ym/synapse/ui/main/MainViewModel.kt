package com.ym.synapse.ui.main

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.ym.synapse.core.AppStateManager
import com.ym.synapse.ui.base.BaseViewModel
import com.ym.synapse.ui.base.UiEffect
import com.ym.synapse.ui.base.UiEvent
import com.ym.synapse.ui.base.UiState
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 主界面ViewModel
 * 使用AppStateManager管理应用状态
 */
class MainViewModel(
    private val appStateManager: AppStateManager
) : BaseViewModel() {

    // UI效果（一次性事件）
    private val _uiEffect = MutableSharedFlow<MainUiEffect>()
    val uiEffect: SharedFlow<MainUiEffect> = _uiEffect.asSharedFlow()

    // 直接暴露AppStateManager的状态流
    val appState = appStateManager.appState
    val permissionState = appStateManager.permissionState
    val initializationState = appStateManager.initializationState

    init {
        // 初始化检查
        checkInitialState()
    }
    
    /**
     * 处理UI事件
     */
    fun handleEvent(event: MainUiEvent) {
        when (event) {
            is MainUiEvent.CheckPermissions -> checkPermissions(event.context)
            is MainUiEvent.NavigateToApiConfig -> navigateToApiConfig()
            is MainUiEvent.NavigateToTutorial -> navigateToTutorial()
            is MainUiEvent.CompleteSetup -> completeSetup()
            is MainUiEvent.ResetConfiguration -> resetConfiguration()
            is MainUiEvent.RefreshPermissions -> refreshPermissions(event.context)
        }
    }

    /**
     * 检查初始状态
     */
    private fun checkInitialState() {
        // AppStateManager会自动处理初始化
    }

    /**
     * 检查权限
     */
    private fun checkPermissions(context: Context) {
        appStateManager.checkPermissions()
    }

    /**
     * 刷新权限状态
     */
    private fun refreshPermissions(context: Context) {
        appStateManager.checkPermissions()
        viewModelScope.launch {
            _uiEffect.emit(MainUiEffect.ShowMessage("权限状态已刷新"))
        }
    }

    /**
     * 导航到API配置
     */
    private fun navigateToApiConfig() {
        appStateManager.updateApiConfigState(true)
        appStateManager.updateTutorialState(false)
    }

    /**
     * 导航到教程
     */
    private fun navigateToTutorial() {
        appStateManager.updateTutorialState(true)
        appStateManager.updateApiConfigState(false)
    }

    /**
     * 完成设置
     */
    private fun completeSetup() {
        appStateManager.completeSetup()
        viewModelScope.launch {
            _uiEffect.emit(MainUiEffect.ShowMessage("设置完成！"))
        }
    }

    /**
     * 重置配置
     */
    private fun resetConfiguration() {
        launchSafely {
            appStateManager.resetApp()
            _uiEffect.emit(MainUiEffect.ShowMessage("配置已重置"))
        }
    }

    /**
     * 获取配置摘要
     */
    fun getAppSummary(): String {
        return appStateManager.getAppSummary()
    }
}

// 使用AppStateManager中的状态类，这里只保留UI特定的状态
// MainUiState 和 PermissionState 现在由 AppStateManager 提供

/**
 * 主界面屏幕枚举
 */
enum class MainScreen {
    Loading,
    PermissionRequest,
    ApiConfig,
    Tutorial,
    Main
}

/**
 * 主界面UI事件
 */
sealed class MainUiEvent : UiEvent {
    data class CheckPermissions(val context: Context) : MainUiEvent()
    data class RefreshPermissions(val context: Context) : MainUiEvent()
    object NavigateToApiConfig : MainUiEvent()
    object NavigateToTutorial : MainUiEvent()
    object CompleteSetup : MainUiEvent()
    object ResetConfiguration : MainUiEvent()
}

/**
 * 主界面UI效果
 */
sealed class MainUiEffect : UiEffect {
    data class ShowMessage(val message: String) : MainUiEffect()
    data class NavigateToScreen(val screen: MainScreen) : MainUiEffect()
    object RequestPermissions : MainUiEffect()
}
