package com.ym.synapse.ui.permission

import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.*
import com.ym.synapse.utils.PermissionHelper

/**
 * 重构后的权限请求界面
 * 更加模块化和可复用
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionRequestScreen(
    permissions: List<PermissionHelper.PermissionInfo>,
    onPermissionGranted: () -> Unit = {},
    onApiConfigClick: () -> Unit = {},
    multiplePermissionsState: MultiplePermissionsState? = null
) {
    val context = LocalContext.current
    var currentPermissions by remember { mutableStateOf(permissions) }
    
    // 创建权限请求状态
    val permissionsToRequest = currentPermissions
        .filter { !it.isGranted && it.permission != "AUTO_START" && it.permission != "BACKGROUND_ACTIVITY" }
        .map { it.permission }

    val actualPermissionsState = multiplePermissionsState ?: rememberMultiplePermissionsState(permissionsToRequest)
    
    // 监听权限状态变化
    LaunchedEffect(actualPermissionsState.allPermissionsGranted) {
        if (actualPermissionsState.allPermissionsGranted) {
            currentPermissions = PermissionHelper.getAllPermissions(context)
            onPermissionGranted()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        PermissionHeader()
        
        Spacer(modifier = Modifier.height(24.dp))
        
        PermissionList(
            permissions = currentPermissions.filter { !it.isGranted },
            modifier = Modifier.weight(1f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        PermissionActions(
            permissions = currentPermissions,
            multiplePermissionsState = actualPermissionsState,
            onRefresh = {
                currentPermissions = PermissionHelper.getAllPermissions(context)
            },
            onApiConfigClick = onApiConfigClick
        )
    }
}

@Composable
private fun PermissionHeader() {
    Icon(
        imageVector = Icons.Filled.Security,
        contentDescription = null,
        modifier = Modifier.size(80.dp),
        tint = MaterialTheme.colorScheme.primary
    )
    
    Spacer(modifier = Modifier.height(24.dp))
    
    Text(
        text = "需要权限授权",
        style = MaterialTheme.typography.headlineMedium,
        fontWeight = FontWeight.Bold,
        textAlign = TextAlign.Center
    )
    
    Spacer(modifier = Modifier.height(16.dp))
    
    Text(
        text = "为了正常使用应用功能，需要您授予以下权限：",
        style = MaterialTheme.typography.bodyLarge,
        textAlign = TextAlign.Center,
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
}

@Composable
private fun PermissionList(
    permissions: List<PermissionHelper.PermissionInfo>,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(permissions) { permission ->
            PermissionItem(permission = permission)
        }
    }
}

@Composable
private fun PermissionItem(
    permission: PermissionHelper.PermissionInfo
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column {
                Text(
                    text = permission.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = permission.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun PermissionActions(
    permissions: List<PermissionHelper.PermissionInfo>,
    multiplePermissionsState: MultiplePermissionsState,
    onRefresh: () -> Unit,
    onApiConfigClick: () -> Unit
) {
    val context = LocalContext.current
    val unGrantedPermissions = permissions.filter { !it.isGranted }
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 基础权限请求按钮
        val standardPermissions = unGrantedPermissions.filter {
            it.permission != "AUTO_START" && it.permission != "BACKGROUND_ACTIVITY"
        }
        
        if (standardPermissions.isNotEmpty()) {
            Button(
                onClick = {
                    multiplePermissionsState.launchMultiplePermissionRequest()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授予基础权限")
            }
        }
        
        // 特殊权限按钮
        SpecialPermissionButtons(
            permissions = unGrantedPermissions,
            onRefresh = onRefresh
        )
        
        // 完成按钮或刷新按钮
        if (unGrantedPermissions.isEmpty()) {
            Button(
                onClick = onApiConfigClick,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Text("前往API配置")
            }
        } else {
            TextButton(
                onClick = onRefresh,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("刷新权限状态")
            }
        }
    }
}

@Composable
private fun SpecialPermissionButtons(
    permissions: List<PermissionHelper.PermissionInfo>,
    onRefresh: () -> Unit
) {
    val context = LocalContext.current
    
    // 自启动权限
    val autoStartPermission = permissions.find { it.permission == "AUTO_START" }
    if (autoStartPermission != null) {
        SpecialPermissionButton(
            text = "设置自启动权限",
            description = "请点击 应用-自启动 开启软件自启动",
            onSettingComplete = {
                PermissionHelper.markAutoStartPermissionGranted(context)
                onRefresh()
            },
            openSettings = {
                PermissionHelper.openAutoStartSettings(context)
            }
        )
    }
    
    // 后台行为权限
    val backgroundPermission = permissions.find { it.permission == "BACKGROUND_ACTIVITY" }
    if (backgroundPermission != null) {
        SpecialPermissionButton(
            text = "设置后台行为权限",
            description = "请点击 耗电管理 选择完全允许后台行为",
            onSettingComplete = {
                PermissionHelper.markBackgroundActivityPermissionGranted(context)
                onRefresh()
            },
            openSettings = {
                PermissionHelper.openAppSettings(context)
            }
        )
    }
}

@Composable
private fun SpecialPermissionButton(
    text: String,
    description: String,
    onSettingComplete: () -> Unit,
    openSettings: () -> android.content.Intent
) {
    val context = LocalContext.current
    var showDialog by remember { mutableStateOf(false) }
    
    OutlinedButton(
        onClick = {
            try {
                val intent = openSettings()
                context.startActivity(intent)
                Toast.makeText(context, description, Toast.LENGTH_LONG).show()
                showDialog = true
            } catch (e: Exception) {
                Toast.makeText(context, "无法打开设置页面", Toast.LENGTH_SHORT).show()
            }
        },
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(text)
    }
    
    if (showDialog) {
        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = { Text("确认设置") },
            text = { Text("您是否已经完成了相关设置？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onSettingComplete()
                        showDialog = false
                    }
                ) {
                    Text("已完成")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}
