package com.ym.synapse.ui.theme

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color

/**
 * 动画主题配置 - 为简约颜值党设计
 */

// 简约配色方案
private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF6750A4),
    onPrimary = Color(0xFFFFFFFF),
    primaryContainer = Color(0xFFEADDFF),
    onPrimaryContainer = Color(0xFF21005D),
    secondary = Color(0xFF625B71),
    onSecondary = Color(0xFFFFFFFF),
    secondaryContainer = Color(0xFFE8DEF8),
    onSecondaryContainer = Color(0xFF1D192B),
    tertiary = Color(0xFF7D5260),
    onTertiary = Color(0xFFFFFFFF),
    tertiaryContainer = Color(0xFFFFD8E4),
    onTertiaryContainer = Color(0xFF31111D),
    error = Color(0xFFBA1A1A),
    errorContainer = Color(0xFFFFDAD6),
    onError = Color(0xFFFFFFFF),
    onErrorContainer = Color(0xFF410002),
    background = Color(0xFFFFFBFE),
    onBackground = Color(0xFF1C1B1F),
    surface = Color(0xFFFFFBFE),
    onSurface = Color(0xFF1C1B1F),
    surfaceVariant = Color(0xFFE7E0EC),
    onSurfaceVariant = Color(0xFF49454F),
    outline = Color(0xFF79747E),
    inverseOnSurface = Color(0xFFF4EFF4),
    inverseSurface = Color(0xFF313033),
    inversePrimary = Color(0xFFD0BCFF),
    surfaceTint = Color(0xFF6750A4),
    outlineVariant = Color(0xFFCAC4D0),
    scrim = Color(0xFF000000),
)

private val DarkColorScheme = darkColorScheme(
    primary = Color(0xFFD0BCFF),
    onPrimary = Color(0xFF381E72),
    primaryContainer = Color(0xFF4F378B),
    onPrimaryContainer = Color(0xFFEADDFF),
    secondary = Color(0xFFCCC2DC),
    onSecondary = Color(0xFF332D41),
    secondaryContainer = Color(0xFF4A4458),
    onSecondaryContainer = Color(0xFFE8DEF8),
    tertiary = Color(0xFFEFB8C8),
    onTertiary = Color(0xFF492532),
    tertiaryContainer = Color(0xFF633B48),
    onTertiaryContainer = Color(0xFFFFD8E4),
    error = Color(0xFFFFB4AB),
    errorContainer = Color(0xFF93000A),
    onError = Color(0xFF690005),
    onErrorContainer = Color(0xFFFFDAD6),
    background = Color(0xFF1C1B1F),
    onBackground = Color(0xFFE6E1E5),
    surface = Color(0xFF1C1B1F),
    onSurface = Color(0xFFE6E1E5),
    surfaceVariant = Color(0xFF49454F),
    onSurfaceVariant = Color(0xFFCAC4D0),
    outline = Color(0xFF938F99),
    inverseOnSurface = Color(0xFF1C1B1F),
    inverseSurface = Color(0xFFE6E1E5),
    inversePrimary = Color(0xFF6750A4),
    surfaceTint = Color(0xFFD0BCFF),
    outlineVariant = Color(0xFF49454F),
    scrim = Color(0xFF000000),
)

/**
 * 动画颜色主题
 */
@Composable
fun AnimatedSynapseTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false, // 暂时禁用动态颜色以保持简约
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    // 动画颜色过渡
    val animatedColorScheme = ColorScheme(
        primary = animateColorAsState(
            targetValue = colorScheme.primary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "primary"
        ).value,
        onPrimary = animateColorAsState(
            targetValue = colorScheme.onPrimary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onPrimary"
        ).value,
        primaryContainer = animateColorAsState(
            targetValue = colorScheme.primaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "primaryContainer"
        ).value,
        onPrimaryContainer = animateColorAsState(
            targetValue = colorScheme.onPrimaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onPrimaryContainer"
        ).value,
        secondary = animateColorAsState(
            targetValue = colorScheme.secondary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "secondary"
        ).value,
        onSecondary = animateColorAsState(
            targetValue = colorScheme.onSecondary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onSecondary"
        ).value,
        secondaryContainer = animateColorAsState(
            targetValue = colorScheme.secondaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "secondaryContainer"
        ).value,
        onSecondaryContainer = animateColorAsState(
            targetValue = colorScheme.onSecondaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onSecondaryContainer"
        ).value,
        tertiary = animateColorAsState(
            targetValue = colorScheme.tertiary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "tertiary"
        ).value,
        onTertiary = animateColorAsState(
            targetValue = colorScheme.onTertiary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onTertiary"
        ).value,
        tertiaryContainer = animateColorAsState(
            targetValue = colorScheme.tertiaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "tertiaryContainer"
        ).value,
        onTertiaryContainer = animateColorAsState(
            targetValue = colorScheme.onTertiaryContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onTertiaryContainer"
        ).value,
        error = animateColorAsState(
            targetValue = colorScheme.error,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "error"
        ).value,
        errorContainer = animateColorAsState(
            targetValue = colorScheme.errorContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "errorContainer"
        ).value,
        onError = animateColorAsState(
            targetValue = colorScheme.onError,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onError"
        ).value,
        onErrorContainer = animateColorAsState(
            targetValue = colorScheme.onErrorContainer,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onErrorContainer"
        ).value,
        background = animateColorAsState(
            targetValue = colorScheme.background,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "background"
        ).value,
        onBackground = animateColorAsState(
            targetValue = colorScheme.onBackground,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onBackground"
        ).value,
        surface = animateColorAsState(
            targetValue = colorScheme.surface,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "surface"
        ).value,
        onSurface = animateColorAsState(
            targetValue = colorScheme.onSurface,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onSurface"
        ).value,
        surfaceVariant = animateColorAsState(
            targetValue = colorScheme.surfaceVariant,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "surfaceVariant"
        ).value,
        onSurfaceVariant = animateColorAsState(
            targetValue = colorScheme.onSurfaceVariant,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "onSurfaceVariant"
        ).value,
        outline = animateColorAsState(
            targetValue = colorScheme.outline,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "outline"
        ).value,
        inverseOnSurface = animateColorAsState(
            targetValue = colorScheme.inverseOnSurface,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "inverseOnSurface"
        ).value,
        inverseSurface = animateColorAsState(
            targetValue = colorScheme.inverseSurface,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "inverseSurface"
        ).value,
        inversePrimary = animateColorAsState(
            targetValue = colorScheme.inversePrimary,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "inversePrimary"
        ).value,
        surfaceTint = animateColorAsState(
            targetValue = colorScheme.surfaceTint,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "surfaceTint"
        ).value,
        outlineVariant = animateColorAsState(
            targetValue = colorScheme.outlineVariant,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "outlineVariant"
        ).value,
        scrim = animateColorAsState(
            targetValue = colorScheme.scrim,
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            label = "scrim"
        ).value,
    )

    MaterialTheme(
        colorScheme = animatedColorScheme,
        typography = Typography,
        content = content
    )
}
