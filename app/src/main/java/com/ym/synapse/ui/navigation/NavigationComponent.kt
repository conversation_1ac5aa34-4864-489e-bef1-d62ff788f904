package com.ym.synapse.ui.navigation


import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.*
import com.ym.synapse.*

/**
 * 导航路由定义
 */
sealed class AppScreen(val route: String, val label: String, val icon: ImageVector) {
    object Home : AppScreen("home", "主页", Icons.Filled.Home)
    object Settings : AppScreen("settings", "设置", Icons.Filled.Settings)
}

val navigationItems = listOf(
    AppScreen.Home,
    AppScreen.Settings
)

/**
 * 主导航组件
 */
@Composable
fun MainNavigationComponent() {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            BottomNavigationBar(navController = navController)
        }
    ) { innerPadding ->
        NavigationHost(
            navController = navController,
            modifier = Modifier.padding(innerPadding)
        )
    }
}

/**
 * 底部导航栏
 */
@Composable
private fun BottomNavigationBar(navController: NavHostController) {
    NavigationBar {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination
        
        navigationItems.forEach { screen ->
            NavigationBarItem(
                icon = { Icon(screen.icon, contentDescription = screen.label) },
                label = { Text(screen.label) },
                selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                onClick = {
                    navController.navigate(screen.route) {
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                }
            )
        }
    }
}

/**
 * 导航主机
 */
@Composable
private fun NavigationHost(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = AppScreen.Home.route,
        modifier = modifier
    ) {
        composable(AppScreen.Home.route) {
            HomeScreenWrapper()
        }

        composable(AppScreen.Settings.route) {
            SettingsScreenWrapper()
        }
    }
}

/**
 * 主页屏幕包装器
 */
@Composable
private fun HomeScreenWrapper() {
    // 使用原有的HomeScreen，但可以在这里添加ViewModel等
    HomeScreen()
}



/**
 * 设置屏幕包装器
 */
@Composable
private fun SettingsScreenWrapper() {
    // 使用新的设置界面
    SettingsScreen()
}

/**
 * 导航状态管理
 */
@Composable
fun rememberNavigationState(): NavigationState {
    return remember { NavigationState() }
}

/**
 * 导航状态类
 */
class NavigationState {
    private val _currentRoute = mutableStateOf(AppScreen.Home.route)
    val currentRoute: State<String> = _currentRoute
    
    fun navigateTo(route: String) {
        _currentRoute.value = route
    }
    
    fun getCurrentScreen(): AppScreen {
        return when (_currentRoute.value) {
            AppScreen.Home.route -> AppScreen.Home
            AppScreen.Settings.route -> AppScreen.Settings
            else -> AppScreen.Home
        }
    }
}
