package com.ym.synapse.service

import android.content.Context
import android.util.Log
import com.ym.synapse.*
import com.ym.synapse.api.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.HttpException
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * 图床服务类
 * 支持多种图床服务
 */
class ImageHostService private constructor() {
    
    companion object {
        private const val TAG = "ImageHostService"
        private const val IMGBB_BASE_URL = "https://api.imgbb.com/"
        private const val SMMS_BASE_URL = "https://sm.ms/api/"
        
        @Volatile
        private var INSTANCE: ImageHostService? = null
        
        fun getInstance(): ImageHostService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ImageHostService().also { INSTANCE = it }
            }
        }
    }
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()
    
    private val imgbbApi = Retrofit.Builder()
        .baseUrl(IMGBB_BASE_URL)
        .client(client)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(ImgBBApiService::class.java)
    
    private val smmsApi = Retrofit.Builder()
        .baseUrl(SMMS_BASE_URL)
        .client(client)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(SmMsApiService::class.java)
    
    /**
     * 上传图片到配置的图床服务
     */
    suspend fun uploadImage(context: Context, imagePath: String): ImageHostResult {
        return withContext(Dispatchers.IO) {
            try {
                val config = getImageHostConfig(context)
                
                if (!config.enabled || !config.isValid()) {
                    return@withContext ImageHostResult.Error("图床服务未启用或配置不完整")
                }
                
                val imageFile = File(imagePath)
                if (!imageFile.exists()) {
                    return@withContext ImageHostResult.Error("图片文件不存在")
                }
                
                Log.d(TAG, "Uploading image to ${config.service}: ${imageFile.name}")
                
                when (config.service) {
                    IMAGE_HOST_IMGBB -> uploadToImgBB(config.apiKey, imageFile)
                    IMAGE_HOST_SMMS -> uploadToSmMs(config.apiKey, imageFile)
                    else -> ImageHostResult.Error("不支持的图床服务: ${config.service}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error uploading image", e)
                ImageHostResult.Error("上传失败: ${e.message}")
            }
        }
    }
    
    /**
     * 上传到ImgBB
     */
    private suspend fun uploadToImgBB(apiKey: String, imageFile: File): ImageHostResult {
        return try {
            val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
            val body = MultipartBody.Part.createFormData("image", imageFile.name, requestFile)
            val name = imageFile.nameWithoutExtension.toRequestBody("text/plain".toMediaTypeOrNull())
            
            val response = imgbbApi.uploadImage(apiKey, body, name)
            
            if (response.isSuccessful) {
                val imgbbResponse = response.body()
                if (imgbbResponse?.success == true && imgbbResponse.data != null) {
                    Log.d(TAG, "ImgBB upload successful: ${imgbbResponse.data.url}")
                    ImageHostResult.Success(
                        url = imgbbResponse.data.url,
                        deleteUrl = imgbbResponse.data.deleteUrl
                    )
                } else {
                    ImageHostResult.Error("ImgBB上传失败: 响应数据无效")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                Log.e(TAG, "ImgBB upload failed: HTTP ${response.code()} - $errorBody")
                ImageHostResult.Error("ImgBB上传失败: HTTP ${response.code()}")
            }
        } catch (e: HttpException) {
            Log.e(TAG, "ImgBB HTTP error", e)
            ImageHostResult.Error("ImgBB上传失败: HTTP ${e.code()}")
        } catch (e: Exception) {
            Log.e(TAG, "ImgBB upload error", e)
            ImageHostResult.Error("ImgBB上传失败: ${e.message}")
        }
    }
    
    /**
     * 上传到SM.MS
     */
    private suspend fun uploadToSmMs(apiKey: String, imageFile: File): ImageHostResult {
        return try {
            val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
            val body = MultipartBody.Part.createFormData("smfile", imageFile.name, requestFile)
            
            val response = smmsApi.uploadImage("Bearer $apiKey", body)
            
            if (response.isSuccessful) {
                val smmsResponse = response.body()
                if (smmsResponse?.success == true && smmsResponse.data != null) {
                    Log.d(TAG, "SM.MS upload successful: ${smmsResponse.data.url}")
                    ImageHostResult.Success(
                        url = smmsResponse.data.url,
                        deleteUrl = smmsResponse.data.delete
                    )
                } else {
                    val errorMsg = smmsResponse?.message ?: "响应数据无效"
                    ImageHostResult.Error("SM.MS上传失败: $errorMsg")
                }
            } else {
                val errorBody = response.errorBody()?.string()
                Log.e(TAG, "SM.MS upload failed: HTTP ${response.code()} - $errorBody")
                ImageHostResult.Error("SM.MS上传失败: HTTP ${response.code()}")
            }
        } catch (e: HttpException) {
            Log.e(TAG, "SM.MS HTTP error", e)
            ImageHostResult.Error("SM.MS上传失败: HTTP ${e.code()}")
        } catch (e: Exception) {
            Log.e(TAG, "SM.MS upload error", e)
            ImageHostResult.Error("SM.MS上传失败: ${e.message}")
        }
    }
    
    /**
     * 获取图床配置
     */
    private fun getImageHostConfig(context: Context): ImageHostConfig {
        val sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        val enabled = sharedPreferences.getBoolean(KEY_IMAGE_HOST_ENABLED, false)
        val service = sharedPreferences.getString(KEY_IMAGE_HOST_SERVICE, IMAGE_HOST_NONE) ?: IMAGE_HOST_NONE
        
        val apiKey = when (service) {
            IMAGE_HOST_IMGBB -> sharedPreferences.getString(KEY_IMGBB_API_KEY, "") ?: ""
            IMAGE_HOST_SMMS -> sharedPreferences.getString(KEY_SMMS_API_KEY, "") ?: ""
            else -> ""
        }
        
        return ImageHostConfig(
            service = service,
            apiKey = apiKey,
            enabled = enabled
        )
    }
    
    /**
     * 测试图床连接
     */
    suspend fun testConnection(context: Context): ImageHostResult {
        return withContext(Dispatchers.IO) {
            try {
                val config = getImageHostConfig(context)
                
                if (!config.enabled || !config.isValid()) {
                    return@withContext ImageHostResult.Error("图床服务未启用或配置不完整")
                }
                
                // 这里可以实现具体的连接测试逻辑
                // 暂时返回成功，实际使用时会在上传时验证
                ImageHostResult.Success("连接测试成功")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error testing connection", e)
                ImageHostResult.Error("连接测试失败: ${e.message}")
            }
        }
    }
}
