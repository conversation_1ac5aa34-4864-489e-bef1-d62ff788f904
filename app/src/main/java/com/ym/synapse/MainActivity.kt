package com.ym.synapse

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.ym.synapse.ui.main.MainViewModel
import com.ym.synapse.ui.main.MainUiEvent
import com.ym.synapse.ui.main.MainUiEffect
import com.ym.synapse.ui.main.MainScreen
import com.ym.synapse.ui.viewModel
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.ResourceManager
import kotlinx.coroutines.flow.collectLatest

/**
 * 重构后的MainActivity
 * 使用MVVM架构，职责更加清晰
 */
class MainActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SynapseTheme {
                MainApp()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        ResourceManager.cleanupAllResources()
    }
}

@Composable
fun MainApp() {
    val context = LocalContext.current
    val viewModel: MainViewModel = viewModel(context)
    
    // 收集UI状态
    val appState by viewModel.appState.collectAsStateWithLifecycle()
    val permissionState by viewModel.permissionState.collectAsStateWithLifecycle()
    val initializationState by viewModel.initializationState.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val error by viewModel.error.collectAsStateWithLifecycle()
    
    // 处理UI效果
    LaunchedEffect(viewModel) {
        viewModel.uiEffect.collectLatest { effect ->
            when (effect) {
                is MainUiEffect.ShowMessage -> {
                    Toast.makeText(context, effect.message, Toast.LENGTH_SHORT).show()
                }
                is MainUiEffect.NavigateToScreen -> {
                    // 处理导航逻辑
                }
                MainUiEffect.RequestPermissions -> {
                    // 处理权限请求
                }
            }
        }
    }
    
    // 初始化时检查权限
    LaunchedEffect(Unit) {
        viewModel.handleEvent(MainUiEvent.CheckPermissions(context))
    }
    
    // 显示错误信息
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            Toast.makeText(context, errorMessage, Toast.LENGTH_LONG).show()
        }
    }
    
    // 根据状态显示不同的界面
    Box(modifier = Modifier.fillMaxSize()) {
        when {
            isLoading || initializationState.isInitializing -> {
                LoadingScreen(initializationState.currentStep)
            }
            appState.shouldShowInitialSetup -> {
                InitialSetupFlow(
                    appState = appState,
                    permissionState = permissionState,
                    onEvent = viewModel::handleEvent
                )
            }
            else -> {
                MainAppContent(
                    onEvent = viewModel::handleEvent
                )
            }
        }
    }
}

@Composable
fun LoadingScreen(currentStep: String = "正在初始化...") {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = currentStep,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun InitialSetupFlow(
    appState: com.ym.synapse.core.AppState,
    permissionState: com.ym.synapse.core.PermissionState,
    onEvent: (MainUiEvent) -> Unit
) {
    val context = LocalContext.current

    when {
        !permissionState.allBasicPermissionsGranted -> {
            com.ym.synapse.ui.permission.PermissionRequestScreen(
                permissions = permissionState.basicPermissions,
                onPermissionGranted = {
                    onEvent(MainUiEvent.RefreshPermissions(context))
                },
                onApiConfigClick = {
                    onEvent(MainUiEvent.NavigateToApiConfig)
                }
            )
        }
        appState.showApiConfig || !appState.isApiConfigured -> {
            ApiConfigScreen(
                onBackClick = {
                    onEvent(MainUiEvent.NavigateToTutorial)
                },
                onConfigComplete = {
                    onEvent(MainUiEvent.NavigateToTutorial)
                }
            )
        }
        appState.showTutorial -> {
            TutorialScreen(
                onBackClick = {
                    onEvent(MainUiEvent.NavigateToApiConfig)
                },
                onComplete = {
                    onEvent(MainUiEvent.CompleteSetup)
                }
            )
        }
        else -> {
            // 设置完成，显示主界面
            MainAppContent(onEvent = onEvent)
        }
    }
}

@Composable
fun MainAppContent(
    onEvent: (MainUiEvent) -> Unit
) {
    // 使用新的导航组件
    com.ym.synapse.ui.navigation.MainNavigationComponent()
}
