package com.ym.synapse

import android.content.Context
import android.util.Log
import android.net.Uri
import androidx.activity.compose.LocalActivityResultRegistryOwner
import androidx.activity.compose.rememberLauncherForActivityResult



import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.*
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.PermissionHelper
import com.google.accompanist.permissions.*
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import android.widget.Toast
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import com.ym.synapse.service.NotionService
import com.ym.synapse.service.NotionResult
import com.ym.synapse.ui.animations.*
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay

// 1. 定义导航路由
sealed class Screen(val route: String, val label: String, val icon: ImageVector) {
    object Home : Screen("home", "主页", Icons.Filled.Home)
    object Settings : Screen("settings", "设置", Icons.Filled.Settings)
}

val items = listOf(
    Screen.Home,
    Screen.Settings
)

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SynapseTheme { // 使用您项目的主题
                MainScreen()
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun MainScreen() {
    val context = LocalContext.current
    val navController = rememberNavController()

    // 权限检测
    val permissionsToRequest = PermissionHelper.getPermissionRequestArray(context).toList()
    val multiplePermissionsState = rememberMultiplePermissionsState(permissionsToRequest)

    // 启动时检查权限
    LaunchedEffect(Unit) {
        if (!PermissionHelper.areAllRequiredPermissionsGranted(context)) {
            multiplePermissionsState.launchMultiplePermissionRequest()
        }
    }

    // 界面状态管理 - 从SharedPreferences恢复状态
    val sharedPreferences = remember {
        context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
    }

    var showApiConfig by remember {
        mutableStateOf(sharedPreferences.getBoolean("show_api_config", false))
    }
    var showTutorial by remember {
        mutableStateOf(sharedPreferences.getBoolean("show_tutorial", false))
    }
    val allPermissionsGranted = PermissionHelper.areAllRequiredPermissionsGranted(context)
    val tutorialCompleted = sharedPreferences.getBoolean("tutorial_completed", false)

    // 保存状态到SharedPreferences
    LaunchedEffect(showApiConfig, showTutorial) {
        with(sharedPreferences.edit()) {
            putBoolean("show_api_config", showApiConfig)
            putBoolean("show_tutorial", showTutorial)
            apply()
        }
    }

    // 调试信息
    LaunchedEffect(allPermissionsGranted, showApiConfig, showTutorial) {
        Log.d("MainActivity", "allPermissionsGranted: $allPermissionsGranted, showApiConfig: $showApiConfig, showTutorial: $showTutorial")
    }

    // 决定显示哪个界面的逻辑
    when {
        tutorialCompleted -> {
        // 显示正常的应用界面
        Scaffold(
            bottomBar = {
                NavigationBar {
                    val navBackStackEntry by navController.currentBackStackEntryAsState()
                    val currentDestination = navBackStackEntry?.destination
                    items.forEach { screen ->
                        NavigationBarItem(
                            icon = { Icon(screen.icon, contentDescription = screen.label) },
                            label = { Text(screen.label) },
                            selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                            onClick = {
                                navController.navigate(screen.route) {
                                    popUpTo(navController.graph.findStartDestination().id) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        )
                    }
                }
            }
        ) { innerPadding ->
            NavHost(navController, startDestination = Screen.Home.route, Modifier.padding(innerPadding)) {
                composable(Screen.Home.route) { HomeScreen() }
                composable(Screen.Settings.route) { NewSettingsScreen() }
            }
        }
        }
        showApiConfig -> {
            // 显示API配置界面
            Log.d("MainActivity", "Showing ApiConfigScreen")
            ApiConfigScreen(
                onBackClick = { showApiConfig = false },
                onConfigComplete = {
                    showApiConfig = false
                    showTutorial = true
                }
            )
        }
        showTutorial -> {
            // 显示使用教程界面
            Log.d("MainActivity", "Showing TutorialScreen")
            TutorialScreen(
                onBackClick = { showTutorial = false },
                onComplete = {
                    showTutorial = false
                    showApiConfig = false
                }
            )
        }
        !allPermissionsGranted -> {
            // 如果权限未授予，显示权限请求界面
            PermissionRequestScreen(
                multiplePermissionsState = multiplePermissionsState,
                onApiConfigClick = {
                    Log.d("MainActivity", "onApiConfigClick called")
                    showApiConfig = true
                }
            )
        }
        else -> {
            // 默认情况：如果没有其他条件匹配，显示API配置界面
            Log.d("MainActivity", "Default case: showing ApiConfigScreen")
            showApiConfig = true
            ApiConfigScreen(
                onBackClick = { showApiConfig = false },
                onConfigComplete = {
                    showApiConfig = false
                    showTutorial = true
                }
            )
        }
    }
}

// --- 权限请求屏幕 ---
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionRequestScreen(
    multiplePermissionsState: MultiplePermissionsState,
    onApiConfigClick: () -> Unit = {}
) {
    val context = LocalContext.current
    var permissions by remember { mutableStateOf(PermissionHelper.getAllPermissions(context)) }

    // 监听应用生命周期，当从设置页面返回时自动刷新权限状态
    LaunchedEffect(Unit) {
        // 定期检查权限状态
        while (true) {
            kotlinx.coroutines.delay(1000) // 每秒检查一次
            val newPermissions = PermissionHelper.getAllPermissions(context)
            if (newPermissions != permissions) {
                permissions = newPermissions
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.Security,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "需要权限授权",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "为了正常使用应用功能，需要您授予以下权限：",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 权限列表 - 只显示未授予的权限
        val unGrantedPermissions = permissions.filter { !it.isGranted }
        unGrantedPermissions.forEach { permission ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = permission.name,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = permission.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 标准权限请求按钮
        val standardPermissions = unGrantedPermissions.filter {
            it.permission != "AUTO_START" && it.permission != "BACKGROUND_ACTIVITY"
        }

        if (standardPermissions.isNotEmpty()) {
            Button(
                onClick = {
                    multiplePermissionsState.launchMultiplePermissionRequest()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授予基础权限")
            }

            Spacer(modifier = Modifier.height(8.dp))
        }

        // 自启动权限按钮
        val autoStartPermission = unGrantedPermissions.find { it.permission == "AUTO_START" }
        if (autoStartPermission != null) {
            var showAutoStartDialog by remember { mutableStateOf(false) }

            OutlinedButton(
                onClick = {
                    try {
                        val intent = PermissionHelper.openAutoStartSettings(context)
                        context.startActivity(intent)
                        Toast.makeText(
                            context,
                            "请点击 应用-自启动 开启软件自启动",
                            Toast.LENGTH_LONG
                        ).show()
                        showAutoStartDialog = true
                    } catch (e: Exception) {
                        // 如果无法打开设置，刷新权限状态
                        permissions = PermissionHelper.getAllPermissions(context)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("设置自启动权限")
            }

            // 确认对话框
            if (showAutoStartDialog) {
                AlertDialog(
                    onDismissRequest = { showAutoStartDialog = false },
                    title = { Text("确认设置") },
                    text = { Text("您是否已经在设置中开启了自启动权限？") },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                PermissionHelper.markAutoStartPermissionGranted(context)
                                permissions = PermissionHelper.getAllPermissions(context)
                                showAutoStartDialog = false
                            }
                        ) {
                            Text("已完成")
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showAutoStartDialog = false }
                        ) {
                            Text("取消")
                        }
                    }
                )
            }

            Spacer(modifier = Modifier.height(8.dp))
        }

        // 允许后台行为权限按钮
        val backgroundPermission = unGrantedPermissions.find { it.permission == "BACKGROUND_ACTIVITY" }
        if (backgroundPermission != null) {
            var showBackgroundDialog by remember { mutableStateOf(false) }

            OutlinedButton(
                onClick = {
                    try {
                        val intent = PermissionHelper.openAppSettings(context)
                        context.startActivity(intent)
                        Toast.makeText(
                            context,
                            "请点击 耗电管理 选择完全允许后台行为",
                            Toast.LENGTH_LONG
                        ).show()
                        showBackgroundDialog = true
                    } catch (e: Exception) {
                        // 如果无法打开设置，刷新权限状态
                        permissions = PermissionHelper.getAllPermissions(context)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("设置后台行为权限")
            }

            // 确认对话框
            if (showBackgroundDialog) {
                AlertDialog(
                    onDismissRequest = { showBackgroundDialog = false },
                    title = { Text("确认设置") },
                    text = { Text("您是否已经在耗电管理中选择了完全允许后台行为？") },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                PermissionHelper.markBackgroundActivityPermissionGranted(context)
                                permissions = PermissionHelper.getAllPermissions(context)
                                showBackgroundDialog = false
                            }
                        ) {
                            Text("已完成")
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showBackgroundDialog = false }
                        ) {
                            Text("取消")
                        }
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 如果所有权限都已授予，显示API配置按钮
        if (unGrantedPermissions.isEmpty()) {
            Button(
                onClick = {
                    android.util.Log.d("MainActivity", "API配置按钮被点击")
                    onApiConfigClick()
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Text("前往API配置")
            }
        } else {
            // 刷新状态按钮
            TextButton(
                onClick = {
                    permissions = PermissionHelper.getAllPermissions(context)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("刷新权限状态")
            }
        }
    }
}

// --- 主页屏幕 ---
@Composable
fun HomeScreen() {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    var historyList by remember { mutableStateOf(emptyList<com.ym.synapse.data.AnalysisRecord>()) }
    var selectedFilter by remember { mutableStateOf("全部") }
    var isRefreshing by remember { mutableStateOf(false) }

    // 刷新数据的函数
    val refreshData = suspend {
        isRefreshing = true
        try {
            // 模拟网络延迟，让用户看到刷新动画
            kotlinx.coroutines.delay(500)
            historyList = com.ym.synapse.data.AnalysisHistoryManager.getHistoryList(context)
        } finally {
            isRefreshing = false
        }
    }

    // 初始加载和自动刷新
    LaunchedEffect(Unit) {
        refreshData()
    }

    // 筛选选项
    val filterOptions = listOf("全部", "Text-Heavy", "Rich-Content", "Simple-Image")

    // 根据筛选条件过滤列表
    val filteredList = if (selectedFilter == "全部") {
        historyList
    } else {
        historyList.filter { it.imageType == selectedFilter }
    }

    val swipeRefreshState = rememberSwipeRefreshState(isRefreshing)

    SwipeRefresh(
        state = swipeRefreshState,
        onRefresh = {
            coroutineScope.launch {
                refreshData()
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
        // 标题和统计信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "分析历史",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "共 ${historyList.size} 条记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 操作按钮
            Row {
                // 刷新按钮
                IconButton(
                    onClick = {
                        coroutineScope.launch {
                            refreshData()
                        }
                        android.widget.Toast.makeText(context, "正在刷新...", android.widget.Toast.LENGTH_SHORT).show()
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                // 空状态时不显示额外按钮，保持界面简洁

                // 清空历史按钮
                if (historyList.isNotEmpty()) {
                    IconButton(
                        onClick = {
                            com.ym.synapse.data.AnalysisHistoryManager.clearHistory(context)
                            coroutineScope.launch {
                                refreshData()
                            }
                            android.widget.Toast.makeText(context, "历史记录已清空", android.widget.Toast.LENGTH_SHORT).show()
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "清空历史",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 筛选标签
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(filterOptions) { filter ->
                FilterChip(
                    onClick = { selectedFilter = filter },
                    label = { Text(filter) },
                    selected = selectedFilter == filter
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 历史记录列表
        if (filteredList.isEmpty()) {
            // 空状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.PhotoLibrary,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = if (selectedFilter == "全部") "暂无分析记录" else "暂无 $selectedFilter 类型记录",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "双击电源键截图开始分析",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            // 历史记录卡片列表
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                itemsIndexed(filteredList) { index, record ->
                    AnimatedListItem(
                        visible = true,
                        index = index
                    ) {
                        AnalysisHistoryCard(
                            record = record,
                            onCardClick = {
                                // 点击卡片打开详细结果
                                val intent = com.ym.synapse.ResultDisplayActivity.createIntent(
                                    context,
                                    record.imageType,
                                    record.resultText,
                                    record.imagePath,
                                    record.notionResult
                                )
                                context.startActivity(intent)
                            },
                            onDeleteClick = {
                                com.ym.synapse.data.AnalysisHistoryManager.deleteRecord(context, record.id)
                                coroutineScope.launch {
                                    refreshData()
                                }
                            },
                            onSendToNotion = if (record.notionResult != null) {
                                {
                                    coroutineScope.launch {
                                        val result = NotionService.getInstance().sendToNotion(
                                            context,
                                            record.notionResult,
                                            record.imageType,
                                            record.imagePath
                                        )

                                        val message = when (result) {
                                            is NotionResult.Success -> "已成功发送到Notion！"
                                            is NotionResult.Error -> "发送失败: ${result.message}"
                                        }

                                        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                                    }
                                }
                            } else null
                        )
                    }
                }
            }
        }
        }
    }
}



@Composable
fun SettingsScreenPlaceholder() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text("设置屏幕 (占位符 - 稍后创建)")
    }
}

// --- 分析历史卡片组件 ---
@Composable
fun AnalysisHistoryCard(
    record: com.ym.synapse.data.AnalysisRecord,
    onCardClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onSendToNotion: (() -> Unit)? = null
) {
    var isPressed by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .bounceAnimation(triggered = isPressed)
            .clickable {
                isPressed = true
                onCardClick()
                // 重置动画状态
                GlobalScope.launch {
                    delay(150)
                    isPressed = false
                }
            },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = record.getAutoTitle(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                    )
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 类型标签
                        Surface(
                            color = when (record.imageType) {
                                "Text-Heavy" -> MaterialTheme.colorScheme.primaryContainer
                                "Rich-Content" -> MaterialTheme.colorScheme.secondaryContainer
                                "Simple-Image" -> MaterialTheme.colorScheme.tertiaryContainer
                                else -> MaterialTheme.colorScheme.surfaceVariant
                            },
                            shape = androidx.compose.foundation.shape.RoundedCornerShape(12.dp),
                            modifier = Modifier.padding(end = 8.dp)
                        ) {
                            Text(
                                text = when (record.imageType) {
                                    "Text-Heavy" -> "文字"
                                    "Rich-Content" -> "富内容"
                                    "Simple-Image" -> "图片"
                                    else -> record.imageType
                                },
                                style = MaterialTheme.typography.labelSmall,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                color = when (record.imageType) {
                                    "Text-Heavy" -> MaterialTheme.colorScheme.onPrimaryContainer
                                    "Rich-Content" -> MaterialTheme.colorScheme.onSecondaryContainer
                                    "Simple-Image" -> MaterialTheme.colorScheme.onTertiaryContainer
                                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }

                        Text(
                            text = record.getFormattedTime(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Row {
                    // Notion按钮（如果有NotionAnalysisResult且提供了回调）
                    if (record.notionResult != null && onSendToNotion != null) {
                        IconButton(onClick = onSendToNotion) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = "发送到Notion",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 预览文本
            Text(
                text = record.getPreviewText(),
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 3,
                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}



@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    SynapseTheme {
        MainScreen()
    }
}