package com.ym.synapse.utils

object AiPrompts {

    const val CLASSIFICATION_PROMPT = """
# 任务
分析提供的截图，并从以下三个类别中选择最符合图片内容的一个。请只返回类别名称，不要添加任何其他解释。

# 类别选项
1.  **Text-Heavy**: 图片的主要内容是连续的文本段落，例如文章、代码、大段笔记、纯文字的设置界面、长篇文档等。文字占据图片的主要部分。

2.  **Rich-Content**: 图片包含复杂的UI元素、图表、数据可视化、照片与文字混排、社交媒体帖子、新闻应用截图、购物应用、地图应用、多媒体内容、带有按钮和图标的界面等。这类图片通常有丰富的视觉元素和交互组件。

3.  **Simple-Image**: 图片主要是单张照片、插画、漫画、艺术作品，或者文字内容非常少、不成段落的简单界面。

# 判断优先级
- 如果图片中有明显的UI元素（按钮、图标、卡片、列表等），优先考虑 Rich-Content
- 如果图片主要是大段连续文字，选择 Text-Heavy
- 如果图片主要是照片或艺术内容，选择 Simple-Image

# 输出格式
直接输出类别名称，例如：
Rich-Content
"""

}
