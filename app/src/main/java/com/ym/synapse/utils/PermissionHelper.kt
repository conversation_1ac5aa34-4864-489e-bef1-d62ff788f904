package com.ym.synapse.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.SharedPreferences
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 * 用于检测和管理应用所需的各种权限
 */
object PermissionHelper {

    private const val PREFS_NAME = "permission_prefs"
    private const val KEY_AUTO_START_GRANTED = "auto_start_granted"
    private const val KEY_BACKGROUND_ACTIVITY_GRANTED = "background_activity_granted"

    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 权限信息数据类
     */
    data class PermissionInfo(
        val permission: String,
        val name: String,
        val description: String,
        val isRequired: Boolean,
        val isGranted: Boolean
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is PermissionInfo) return false
            return permission == other.permission && isGranted == other.isGranted
        }

        override fun hashCode(): Int {
            return permission.hashCode() * 31 + isGranted.hashCode()
        }
    }

    /**
     * 检查通知权限
     */
    fun checkNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 13以下版本默认有通知权限
            true
        }
    }

    /**
     * 检查相册读取权限
     */
    fun checkMediaPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13及以上使用READ_MEDIA_IMAGES
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查文件读取权限
     */
    fun checkStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13及以上，检查READ_MEDIA_IMAGES权限
            checkMediaPermission(context)
        } else {
            // Android 12及以下，检查READ_EXTERNAL_STORAGE权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查允许后台行为权限
     * 注意：这个权限无法通过API直接检测，使用本地存储记录
     */
    fun checkBackgroundActivityPermission(context: Context): Boolean {
        return getPreferences(context).getBoolean(KEY_BACKGROUND_ACTIVITY_GRANTED, false)
    }

    /**
     * 标记后台行为权限为已授予
     */
    fun markBackgroundActivityPermissionGranted(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_BACKGROUND_ACTIVITY_GRANTED, true)
            .apply()
    }

    /**
     * 检查自启动权限（厂商特定）
     * 注意：这个权限无法通过API直接检测，使用本地存储记录
     */
    fun checkAutoStartPermission(context: Context): Boolean {
        return getPreferences(context).getBoolean(KEY_AUTO_START_GRANTED, false)
    }

    /**
     * 标记自启动权限为已授予
     */
    fun markAutoStartPermissionGranted(context: Context) {
        getPreferences(context).edit()
            .putBoolean(KEY_AUTO_START_GRANTED, true)
            .apply()
    }

    /**
     * 打开应用设置页面（用于后台行为设置）
     */
    fun openAppSettings(context: Context): Intent {
        return Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = android.net.Uri.parse("package:${context.packageName}")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }

    /**
     * 检查Intent是否可以被处理
     */
    private fun isIntentAvailable(context: Context, intent: Intent): Boolean {
        return try {
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            activities.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 打开设置页面（用于自启动设置）
     */
    fun openAutoStartSettings(context: Context): Intent {
        // 直接跳转到通用设置页面
        return Intent(Settings.ACTION_SETTINGS).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }

    /**
     * 获取所有需要检测的权限列表
     */
    fun getAllPermissions(context: Context): List<PermissionInfo> {
        val permissions = mutableListOf<PermissionInfo>()

        // 通知权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(
                PermissionInfo(
                    permission = Manifest.permission.POST_NOTIFICATIONS,
                    name = "通知权限",
                    description = "用于显示截图处理结果和状态通知",
                    isRequired = true,
                    isGranted = checkNotificationPermission(context)
                )
            )
        }

        // 相册读取权限
        val mediaPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }

        permissions.add(
            PermissionInfo(
                permission = mediaPermission,
                name = "相册读取权限",
                description = "用于读取和处理截图文件",
                isRequired = true,
                isGranted = checkMediaPermission(context)
            )
        )

        // 自启动权限
        permissions.add(
            PermissionInfo(
                permission = "AUTO_START",
                name = "自启动权限",
                description = "允许应用开机自启动，确保服务持续运行",
                isRequired = true,
                isGranted = checkAutoStartPermission(context)
            )
        )

        // 允许后台行为权限
        permissions.add(
            PermissionInfo(
                permission = "BACKGROUND_ACTIVITY",
                name = "允许后台行为",
                description = "允许应用完全在后台运行，确保截图功能正常工作",
                isRequired = true,
                isGranted = checkBackgroundActivityPermission(context)
            )
        )

        return permissions
    }

    /**
     * 检查是否所有必需权限都已授予
     */
    fun areAllRequiredPermissionsGranted(context: Context): Boolean {
        return getAllPermissions(context).filter { it.isRequired }.all { it.isGranted }
    }

    /**
     * 获取未授予的必需权限列表
     */
    fun getMissingRequiredPermissions(context: Context): List<PermissionInfo> {
        return getAllPermissions(context).filter { it.isRequired && !it.isGranted }
    }

    /**
     * 获取权限请求数组（用于ActivityResultContracts.RequestMultiplePermissions）
     */
    fun getPermissionRequestArray(context: Context): Array<String> {
        return getAllPermissions(context)
            .filter { it.isRequired && !it.isGranted }
            .map { it.permission }
            .toTypedArray()
    }
}
