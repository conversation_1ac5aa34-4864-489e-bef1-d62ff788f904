package com.ym.synapse.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import java.io.Closeable
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 资源管理工具类
 * 统一管理应用中的各种资源，防止内存泄漏
 */
object ResourceManager {
    
    private const val TAG = "ResourceManager"
    
    // 跟踪所有活跃的资源
    private val activeResources = ConcurrentHashMap<String, ManagedResource>()
    private val isShuttingDown = AtomicBoolean(false)
    
    /**
     * 托管资源接口
     */
    interface ManagedResource {
        val resourceId: String
        fun cleanup()
        fun isActive(): Boolean
    }
    
    /**
     * 协程作业资源包装器
     */
    class CoroutineJobResource(
        override val resourceId: String,
        private val job: Job,
        private val scope: CoroutineScope? = null
    ) : ManagedResource {
        
        override fun cleanup() {
            try {
                if (job.isActive) {
                    job.cancel()
                    Log.d(TAG, "Cancelled job: $resourceId")
                }
                scope?.cancel()
            } catch (e: Exception) {
                Log.w(TAG, "Error cleaning up job resource: $resourceId", e)
            }
        }
        
        override fun isActive(): Boolean = job.isActive
    }
    
    /**
     * 可关闭资源包装器
     */
    class CloseableResource(
        override val resourceId: String,
        private val closeable: Closeable
    ) : ManagedResource {
        
        private val isClosed = AtomicBoolean(false)
        
        override fun cleanup() {
            if (!isClosed.getAndSet(true)) {
                try {
                    closeable.close()
                    Log.d(TAG, "Closed resource: $resourceId")
                } catch (e: Exception) {
                    Log.w(TAG, "Error closing resource: $resourceId", e)
                }
            }
        }
        
        override fun isActive(): Boolean = !isClosed.get()
    }
    
    /**
     * 自定义资源包装器
     */
    class CustomResource(
        override val resourceId: String,
        private val cleanupAction: () -> Unit,
        private val activeCheck: () -> Boolean = { true }
    ) : ManagedResource {
        
        private val isCleanedUp = AtomicBoolean(false)
        
        override fun cleanup() {
            if (!isCleanedUp.getAndSet(true)) {
                try {
                    cleanupAction()
                    Log.d(TAG, "Cleaned up custom resource: $resourceId")
                } catch (e: Exception) {
                    Log.w(TAG, "Error cleaning up custom resource: $resourceId", e)
                }
            }
        }
        
        override fun isActive(): Boolean = !isCleanedUp.get() && activeCheck()
    }
    
    /**
     * 注册资源进行管理
     */
    fun registerResource(resource: ManagedResource) {
        if (isShuttingDown.get()) {
            Log.w(TAG, "Cannot register resource during shutdown: ${resource.resourceId}")
            return
        }
        
        activeResources[resource.resourceId] = resource
        Log.d(TAG, "Registered resource: ${resource.resourceId}")
    }
    
    /**
     * 注册协程作业
     */
    fun registerJob(resourceId: String, job: Job, scope: CoroutineScope? = null) {
        registerResource(CoroutineJobResource(resourceId, job, scope))
    }
    
    /**
     * 注册可关闭资源
     */
    fun registerCloseable(resourceId: String, closeable: Closeable) {
        registerResource(CloseableResource(resourceId, closeable))
    }
    
    /**
     * 注册自定义资源
     */
    fun registerCustom(
        resourceId: String,
        cleanupAction: () -> Unit,
        activeCheck: () -> Boolean = { true }
    ) {
        registerResource(CustomResource(resourceId, cleanupAction, activeCheck))
    }
    
    /**
     * 手动清理特定资源
     */
    fun cleanupResource(resourceId: String): Boolean {
        val resource = activeResources.remove(resourceId)
        return if (resource != null) {
            resource.cleanup()
            Log.d(TAG, "Manually cleaned up resource: $resourceId")
            true
        } else {
            Log.w(TAG, "Resource not found for cleanup: $resourceId")
            false
        }
    }
    
    /**
     * 清理所有非活跃资源
     */
    fun cleanupInactiveResources() {
        val inactiveResources = activeResources.filter { !it.value.isActive() }
        
        inactiveResources.forEach { (resourceId, resource) ->
            activeResources.remove(resourceId)
            resource.cleanup()
            Log.d(TAG, "Cleaned up inactive resource: $resourceId")
        }
        
        if (inactiveResources.isNotEmpty()) {
            Log.i(TAG, "Cleaned up ${inactiveResources.size} inactive resources")
        }
    }
    
    /**
     * 清理所有资源（应用关闭时调用）
     */
    fun cleanupAllResources() {
        isShuttingDown.set(true)
        
        val resourceCount = activeResources.size
        Log.i(TAG, "Starting cleanup of $resourceCount resources")
        
        activeResources.values.forEach { resource ->
            try {
                resource.cleanup()
            } catch (e: Exception) {
                Log.e(TAG, "Error during resource cleanup: ${resource.resourceId}", e)
            }
        }
        
        activeResources.clear()
        Log.i(TAG, "Completed cleanup of all resources")
    }
    
    /**
     * 获取资源状态报告
     */
    fun getResourceReport(): String {
        val activeCount = activeResources.count { it.value.isActive() }
        val totalCount = activeResources.size
        
        return buildString {
            appendLine("Resource Manager Report:")
            appendLine("Total registered resources: $totalCount")
            appendLine("Active resources: $activeCount")
            appendLine("Inactive resources: ${totalCount - activeCount}")
            appendLine("Is shutting down: ${isShuttingDown.get()}")
            
            if (activeResources.isNotEmpty()) {
                appendLine("\nActive resources:")
                activeResources.forEach { (id, resource) ->
                    appendLine("  - $id (active: ${resource.isActive()})")
                }
            }
        }
    }
    
    /**
     * 检查是否有资源泄漏
     */
    fun checkForLeaks(context: Context? = null) {
        val inactiveResources = activeResources.filter { !it.value.isActive() }
        
        if (inactiveResources.isNotEmpty()) {
            val message = "Detected ${inactiveResources.size} potential resource leaks"
            Log.w(TAG, message)
            
            ErrorHandler.handleResourceLeakError(
                context = context,
                message = message,
                contextInfo = "ResourceManager.checkForLeaks"
            )
            
            // 自动清理泄漏的资源
            cleanupInactiveResources()
        }
    }
    
    /**
     * 定期检查资源状态（可在后台定期调用）
     */
    fun performMaintenanceCheck(context: Context? = null) {
        Log.d(TAG, "Performing maintenance check")
        
        // 检查资源泄漏
        checkForLeaks(context)
        
        // 记录资源状态
        Log.d(TAG, getResourceReport())
    }
}
