package com.ym.synapse.utils

import android.content.Context
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 统一的错误处理工具类
 * 提供一致的错误处理、日志记录和用户反馈机制
 */
object ErrorHandler {
    
    private const val TAG = "ErrorHandler"
    
    /**
     * 错误类型枚举
     */
    enum class ErrorType {
        NETWORK,           // 网络错误
        PERMISSION,        // 权限错误
        FILE_IO,          // 文件IO错误
        ROOT_ACCESS,      // Root权限错误
        API_ERROR,        // API调用错误
        RESOURCE_LEAK,    // 资源泄漏
        UNKNOWN           // 未知错误
    }
    
    /**
     * 错误信息数据类
     */
    data class ErrorInfo(
        val type: ErrorType,
        val message: String,
        val exception: Throwable? = null,
        val context: String? = null,
        val showToUser: Boolean = true
    )
    
    /**
     * 处理错误的主要方法
     */
    fun handleError(
        context: Context?,
        errorInfo: ErrorInfo,
        onRetry: (() -> Unit)? = null
    ) {
        // 记录详细日志
        logError(errorInfo)
        
        // 向用户显示友好的错误信息
        if (errorInfo.showToUser && context != null) {
            showUserFriendlyError(context, errorInfo, onRetry)
        }
    }
    
    /**
     * 记录错误日志
     */
    private fun logError(errorInfo: ErrorInfo) {
        val logMessage = buildString {
            append("Error Type: ${errorInfo.type}")
            append(", Message: ${errorInfo.message}")
            errorInfo.context?.let { append(", Context: $it") }
        }
        
        when (errorInfo.type) {
            ErrorType.NETWORK, ErrorType.API_ERROR -> {
                Log.e(TAG, logMessage, errorInfo.exception)
            }
            ErrorType.PERMISSION, ErrorType.ROOT_ACCESS -> {
                Log.w(TAG, logMessage, errorInfo.exception)
            }
            ErrorType.RESOURCE_LEAK -> {
                Log.e(TAG, "CRITICAL: $logMessage", errorInfo.exception)
            }
            else -> {
                Log.e(TAG, logMessage, errorInfo.exception)
            }
        }
    }
    
    /**
     * 向用户显示友好的错误信息
     */
    private fun showUserFriendlyError(
        context: Context,
        errorInfo: ErrorInfo,
        onRetry: (() -> Unit)?
    ) {
        val userMessage = getUserFriendlyMessage(errorInfo)
        
        CoroutineScope(Dispatchers.Main).launch {
            Toast.makeText(context, userMessage, Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 获取用户友好的错误信息
     */
    private fun getUserFriendlyMessage(errorInfo: ErrorInfo): String {
        return when (errorInfo.type) {
            ErrorType.NETWORK -> "网络连接失败，请检查网络设置"
            ErrorType.PERMISSION -> "权限不足，请检查应用权限设置"
            ErrorType.FILE_IO -> "文件操作失败，请检查存储空间"
            ErrorType.ROOT_ACCESS -> "需要Root权限，请确保设备已获取Root权限"
            ErrorType.API_ERROR -> "API调用失败，请检查配置或稍后重试"
            ErrorType.RESOURCE_LEAK -> "系统资源不足，建议重启应用"
            ErrorType.UNKNOWN -> "发生未知错误：${errorInfo.message}"
        }
    }
    
    /**
     * 创建协程异常处理器
     */
    fun createCoroutineExceptionHandler(
        context: Context?,
        contextInfo: String = ""
    ): CoroutineExceptionHandler {
        return CoroutineExceptionHandler { _, exception ->
            val errorInfo = ErrorInfo(
                type = when (exception) {
                    is java.net.SocketTimeoutException,
                    is java.net.UnknownHostException,
                    is java.io.IOException -> ErrorType.NETWORK
                    is SecurityException -> ErrorType.PERMISSION
                    else -> ErrorType.UNKNOWN
                },
                message = exception.message ?: "Unknown error",
                exception = exception,
                context = contextInfo
            )
            
            handleError(context, errorInfo)
        }
    }
    
    /**
     * 网络错误处理的便捷方法
     */
    fun handleNetworkError(
        context: Context?,
        exception: Throwable,
        contextInfo: String = ""
    ) {
        val errorInfo = ErrorInfo(
            type = ErrorType.NETWORK,
            message = exception.message ?: "Network error",
            exception = exception,
            context = contextInfo
        )
        handleError(context, errorInfo)
    }
    
    /**
     * 权限错误处理的便捷方法
     */
    fun handlePermissionError(
        context: Context?,
        message: String,
        contextInfo: String = ""
    ) {
        val errorInfo = ErrorInfo(
            type = ErrorType.PERMISSION,
            message = message,
            context = contextInfo
        )
        handleError(context, errorInfo)
    }
    
    /**
     * Root权限错误处理的便捷方法
     */
    fun handleRootError(
        context: Context?,
        message: String,
        contextInfo: String = ""
    ) {
        val errorInfo = ErrorInfo(
            type = ErrorType.ROOT_ACCESS,
            message = message,
            context = contextInfo
        )
        handleError(context, errorInfo)
    }
    
    /**
     * 资源泄漏错误处理的便捷方法
     */
    fun handleResourceLeakError(
        context: Context?,
        message: String,
        exception: Throwable? = null,
        contextInfo: String = ""
    ) {
        val errorInfo = ErrorInfo(
            type = ErrorType.RESOURCE_LEAK,
            message = message,
            exception = exception,
            context = contextInfo,
            showToUser = false // 资源泄漏通常不需要向用户显示
        )
        handleError(context, errorInfo)
    }
}
