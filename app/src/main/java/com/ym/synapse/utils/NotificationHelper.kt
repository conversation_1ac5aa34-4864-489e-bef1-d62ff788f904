package com.ym.synapse.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.ym.synapse.R

object NotificationHelper {

    private const val CHANNEL_ID = "ai_ocr_channel"
    private const val CHANNEL_NAME = "AI OCR 通知"
    private const val NOTIFICATION_ID = 1 // Match ScreenshotService foreground notification ID for replacement

    fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "用于 AI OCR 结果的通知"
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            android.util.Log.d("NotificationHelper", "Notification channel created: $CHANNEL_ID")
        }
    }

    private fun getNotificationManager(context: Context): NotificationManager {
        return context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    fun showSilentNotification(context: Context, title: String, message: String) {
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW) // Lower priority for silent notifications
            .setAutoCancel(true)

        getNotificationManager(context).notify(NOTIFICATION_ID, builder.build())
    }

    fun showProcessingNotification(context: Context, title: String, message: String) {
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setOngoing(true) // Keep it visible while processing
            .setProgress(0, 0, true) // Indeterminate progress bar

        getNotificationManager(context).notify(NOTIFICATION_ID, builder.build())
    }

    fun showResultNotification(context: Context, title: String, content: String) {
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(NotificationCompat.BigTextStyle().bigText(content)) // Allow long text
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true) // Dismiss on tap

        getNotificationManager(context).notify(NOTIFICATION_ID, builder.build())
    }

    fun showResultNotificationWithAction(context: Context, title: String, content: String, actionIntent: Intent) {
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            actionIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(content)
            .setStyle(NotificationCompat.BigTextStyle().bigText(content))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent) // Click action
            .setDefaults(NotificationCompat.DEFAULT_ALL) // 添加默认声音、震动等
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // 确保通知可见

        val notificationManager = getNotificationManager(context)
        val resultNotificationId = NOTIFICATION_ID + 1 // 使用不同的ID避免被覆盖

        android.util.Log.d("NotificationHelper", "Showing result notification with ID: $resultNotificationId")
        android.util.Log.d("NotificationHelper", "Title: $title, Content: $content")

        notificationManager.notify(resultNotificationId, builder.build())
    }
}
