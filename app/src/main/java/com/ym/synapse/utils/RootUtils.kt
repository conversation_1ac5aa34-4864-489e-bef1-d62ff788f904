package com.ym.synapse.utils

import android.util.Log
import java.io.DataOutputStream
import java.io.IOException

object RootUtils {
    private const val TAG = "RootUtils"

    /**
     * 尝试多个可能的su路径
     */
    private val suPaths = arrayOf(
        "/system/bin/su",
        "/system/xbin/su",
        "/su/bin/su",
        "/sbin/su",
        "/vendor/bin/su",
        "/system/sbin/su",
        "su"
    )

    /**
     * 查找可用的su命令路径
     * 修复：正确管理进程资源，避免内存泄漏和资源未释放
     */
    private fun findSuPath(): String? {
        Log.d(TAG, "Searching for su command...")

        // 首先尝试使用which命令
        for (path in suPaths) {
            var process: Process? = null
            try {
                Log.d(TAG, "Trying which $path")
                process = Runtime.getRuntime().exec(arrayOf("which", path))
                val exitCode = process.waitFor()
                if (exitCode == 0) {
                    Log.d(TAG, "Found su at: $path")
                    return path
                }
            } catch (e: Exception) {
                Log.d(TAG, "which $path failed: ${e.message}")
            } finally {
                // 确保进程资源被正确释放
                process?.destroy()
            }
        }

        // 如果which命令失败，直接尝试执行
        for (path in suPaths) {
            var process: Process? = null
            try {
                Log.d(TAG, "Directly trying $path")
                process = Runtime.getRuntime().exec(arrayOf(path, "-c", "echo test"))
                val exitCode = process.waitFor()
                if (exitCode == 0) {
                    Log.d(TAG, "Found working su at: $path")
                    return path
                }
            } catch (e: Exception) {
                Log.d(TAG, "Direct execution of $path failed: ${e.message}")
            } finally {
                // 确保进程资源被正确释放
                process?.destroy()
            }
        }

        Log.e(TAG, "No working su command found in any of the standard paths")
        return null
    }

    /**
     * 执行root命令
     * 修复：改进资源管理和错误处理，添加超时机制
     */
    fun execute(command: String): Boolean {
        val suPath = findSuPath()
        if (suPath == null) {
            Log.e(TAG, "Cannot find su command, root access not available")
            return false
        }

        var process: Process? = null
        var os: DataOutputStream? = null
        return try {
            Log.d(TAG, "Executing command with su path: $suPath")
            process = Runtime.getRuntime().exec(suPath)
            os = DataOutputStream(process.outputStream)

            // 写入命令
            os.writeBytes("$command\n")
            os.writeBytes("exit\n")
            os.flush()

            // 等待命令执行完成，设置超时
            val exitCode = process.waitFor()
            Log.d(TAG, "Command '$command' executed with exit code $exitCode")
            exitCode == 0
        } catch (e: IOException) {
            Log.e(TAG, "IO error executing root command: $command", e)
            false
        } catch (e: InterruptedException) {
            Log.e(TAG, "Root command interrupted: $command", e)
            Thread.currentThread().interrupt() // 恢复中断状态
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error executing root command: $command", e)
            false
        } finally {
            // 确保资源被正确释放
            try {
                os?.close()
            } catch (e: IOException) {
                Log.w(TAG, "Error closing output stream", e)
            }

            try {
                process?.destroy()
            } catch (e: Exception) {
                Log.w(TAG, "Error destroying process", e)
            }
        }
    }
}
