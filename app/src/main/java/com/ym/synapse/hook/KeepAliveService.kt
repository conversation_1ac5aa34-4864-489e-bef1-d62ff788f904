package com.ym.synapse.hook

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log

/**
 * A lightweight service designed to be started by an external process (like a Magisk module)
 * with root privileges. Its sole purpose is to bring the application's process to life,
 * allowing it to receive broadcasts and perform background tasks even if killed by the OS.
 */
class KeepAliveService : Service() {

    companion object {
        const val TAG = "KeepAliveService"
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "KeepAliveService created. The application process is being activated.")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "KeepAliveService started, likely by the Magisk boot script. The process should now be active.")
        // Return START_STICKY to request that the system restart the service if it's killed.
        // This provides an extra layer of persistence.
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.w(TAG, "KeepAliveService is being destroyed. The process may be terminated by the system.")
    }

    override fun onBind(intent: Intent?): IBinder? {
        // This is not a bound service.
        return null
    }
}
