package com.ym.synapse.hook

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class BootCompletedReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootCompletedReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.e(TAG, "!!!!!!!! BootCompletedReceiver: onReceive CALLED - BOOT COMPLETED BROADCAST RECEIVED !!!!!!!!") // Force log for debug
        if (Intent.ACTION_BOOT_COMPLETED == intent.action) {
            Log.i(TAG, "Boot completed event received. Attempting to start KeepAliveService and schedule BootTasksWorker.")
            try {
                // Start KeepAliveService directly
                val serviceIntent = Intent(context, KeepAliveService::class.java)
                context.startService(serviceIntent)
                Log.i(TAG, "KeepAliveService started directly.")
            } catch (e: Exception) {
                Log.e(TAG, "Error starting KeepAliveService", e)
            }
        }
    }
}
