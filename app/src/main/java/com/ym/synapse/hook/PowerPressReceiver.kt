package com.ym.synapse.hook

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class PowerPressReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "PowerPressReceiver"
        private var lastTriggerTime = 0L
        private const val MIN_TRIGGER_INTERVAL = 2000L // 2秒内不允许重复触发
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == HookEntry.ACTION_DOUBLE_PRESS_POWER) {
            val currentTime = System.currentTimeMillis()

            // 防止重复触发
            if (currentTime - lastTriggerTime < MIN_TRIGGER_INTERVAL) {
                Log.d(TAG, "Ignoring duplicate trigger within ${MIN_TRIGGER_INTERVAL}ms")
                return
            }

            lastTriggerTime = currentTime
            Log.d(TAG, "Received double power press broadcast!")

            // 标记截图已触发，用于教程界面停止倒计时
            val sharedPreferences = context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
            with(sharedPreferences.edit()) {
                putBoolean("screenshot_triggered", true)
                putLong("screenshot_trigger_time", currentTime)
                apply()
            }

            // Start the background service to handle screenshot logic
            val serviceIntent = Intent(context, ScreenshotService::class.java)
            context.startService(serviceIntent)
            Log.d(TAG, "Starting ScreenshotService.")
        }
    }
}
