package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.ui.components.MarkdownText
import com.ym.synapse.ui.components.isMarkdownContent
import com.ym.synapse.ui.components.enhanceTextWithMarkdown
import com.ym.synapse.data.NotionAnalysisResult
import com.ym.synapse.service.NotionService
import com.ym.synapse.service.NotionResult
import kotlinx.coroutines.launch
import android.widget.Toast

class ResultDisplayActivity : ComponentActivity() {
    
    companion object {
        const val EXTRA_IMAGE_TYPE = "image_type"
        const val EXTRA_RESULT_TEXT = "result_text"
        const val EXTRA_IMAGE_PATH = "image_path"
        const val EXTRA_NOTION_RESULT = "notion_result"

        fun createIntent(context: Context, imageType: String, resultText: String, imagePath: String, notionResult: NotionAnalysisResult? = null): Intent {
            return Intent(context, ResultDisplayActivity::class.java).apply {
                putExtra(EXTRA_IMAGE_TYPE, imageType)
                putExtra(EXTRA_RESULT_TEXT, resultText)
                putExtra(EXTRA_IMAGE_PATH, imagePath)
                if (notionResult != null) {
                    putExtra(EXTRA_NOTION_RESULT, com.google.gson.Gson().toJson(notionResult))
                }
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val imageType = intent.getStringExtra(EXTRA_IMAGE_TYPE) ?: "未知类型"
        val resultText = intent.getStringExtra(EXTRA_RESULT_TEXT) ?: "无结果"
        val imagePath = intent.getStringExtra(EXTRA_IMAGE_PATH) ?: ""

        // 解析NotionAnalysisResult
        val notionResult = intent.getStringExtra(EXTRA_NOTION_RESULT)?.let { json ->
            try {
                com.google.gson.Gson().fromJson(json, NotionAnalysisResult::class.java)
            } catch (e: Exception) {
                null
            }
        }

        setContent {
            SynapseTheme {
                ResultDisplayScreen(
                    imageType = imageType,
                    resultText = resultText,
                    imagePath = imagePath,
                    notionResult = notionResult,
                    onClose = { finish() },
                    onShare = { shareResult(imageType, resultText) },
                    onSendToNotion = if (notionResult != null) {
                        { sendToNotion(notionResult, imagePath) }
                    } else null
                )
            }
        }
    }
    
    private fun shareResult(imageType: String, resultText: String) {
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, "图片分析结果\n\n类型：$imageType\n\n内容：\n$resultText")
            putExtra(Intent.EXTRA_SUBJECT, "Synapse 图片分析结果")
        }
        startActivity(Intent.createChooser(shareIntent, "分享分析结果"))
    }

    private fun sendToNotion(notionResult: NotionAnalysisResult, imagePath: String) {
        // 这里需要在协程中调用，但由于这是Activity方法，我们需要在Composable中处理
        // 实际的发送逻辑将在Composable的协程中处理
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResultDisplayScreen(
    imageType: String,
    resultText: String,
    imagePath: String,
    notionResult: NotionAnalysisResult? = null,
    onClose: () -> Unit,
    onShare: () -> Unit,
    onSendToNotion: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("分析结果") },
                navigationIcon = {
                    IconButton(onClick = onClose) {
                        Icon(Icons.Default.Close, contentDescription = "关闭")
                    }
                },
                actions = {
                    // Notion按钮
                    if (notionResult != null) {
                        IconButton(
                            onClick = {
                                coroutineScope.launch {
                                    val result = NotionService.getInstance().sendToNotion(
                                        context,
                                        notionResult,
                                        imageType,
                                        imagePath
                                    )

                                    val message = when (result) {
                                        is NotionResult.Success -> "已成功发送到Notion！"
                                        is NotionResult.Error -> "发送失败: ${result.message}"
                                    }

                                    Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                                }
                            }
                        ) {
                            Icon(Icons.Default.Send, contentDescription = "发送到Notion")
                        }
                    }

                    IconButton(onClick = onShare) {
                        Icon(Icons.Default.Share, contentDescription = "分享")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 图片类型卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "图片类型",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = imageType,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
            
            // 分析结果卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "分析结果",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(12.dp))

                    // 根据内容类型和是否包含 Markdown 格式选择显示方式
                    val enhancedText = enhanceTextWithMarkdown(resultText, imageType)
                    val useMarkdown = isMarkdownContent(enhancedText) || imageType == "Text-Heavy"

                    if (useMarkdown) {
                        MarkdownText(
                            markdown = enhancedText,
                            modifier = Modifier.fillMaxWidth(),
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    } else {
                        Text(
                            text = resultText,
                            style = MaterialTheme.typography.bodyMedium,
                            lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.4
                        )
                    }
                }
            }
            
            // 图片路径信息（调试用）
            if (imagePath.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "图片路径",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = imagePath,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            
            // 底部间距
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}
