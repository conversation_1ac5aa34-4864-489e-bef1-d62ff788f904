package com.ym.synapse

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.permissions.*
import com.ym.synapse.utils.PermissionHelper
import com.ym.synapse.ui.theme.SynapseTheme

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionScreen() {
    val context = LocalContext.current
    var permissions by remember { mutableStateOf(PermissionHelper.getAllPermissions(context)) }

    // 使用Accompanist权限库
    val permissionsToRequest = permissions.filter { !it.isGranted }.map { it.permission }
    val multiplePermissionsState = rememberMultiplePermissionsState(permissionsToRequest)

    // 监听权限状态变化
    LaunchedEffect(multiplePermissionsState.allPermissionsGranted) {
        permissions = PermissionHelper.getAllPermissions(context)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Top
    ) {
        // 标题
        Text(
            text = "权限检测",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 权限状态概览卡片
        PermissionOverviewCard(permissions = permissions)

        Spacer(modifier = Modifier.height(16.dp))

        // 权限详细列表
        Text(
            text = "权限详情",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.weight(1f)
        ) {
            items(permissions) { permission ->
                PermissionItem(
                    permission = permission,
                    onRequestPermission = {
                        if (!permission.isGranted) {
                            multiplePermissionsState.launchMultiplePermissionRequest()
                        }
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 底部操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 刷新按钮
            OutlinedButton(
                onClick = {
                    permissions = PermissionHelper.getAllPermissions(context)
                },
                modifier = Modifier.weight(1f)
            ) {
                Text("刷新状态")
            }

            // 请求所有权限按钮
            Button(
                onClick = {
                    multiplePermissionsState.launchMultiplePermissionRequest()
                },
                enabled = !PermissionHelper.areAllRequiredPermissionsGranted(context),
                modifier = Modifier.weight(1f)
            ) {
                Text("请求权限")
            }
        }
    }
}

@Composable
fun PermissionOverviewCard(permissions: List<PermissionHelper.PermissionInfo>) {
    val grantedCount = permissions.count { it.isGranted }
    val totalCount = permissions.size
    val allGranted = grantedCount == totalCount

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (allGranted) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (allGranted) Icons.Default.Check else Icons.Default.Warning,
                contentDescription = null,
                tint = if (allGranted) 
                    MaterialTheme.colorScheme.onPrimaryContainer 
                else 
                    MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column {
                Text(
                    text = if (allGranted) "所有权限已授予" else "需要授予权限",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (allGranted) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onErrorContainer
                )
                Text(
                    text = "已授予 $grantedCount/$totalCount 项权限",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (allGranted) 
                        MaterialTheme.colorScheme.onPrimaryContainer 
                    else 
                        MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

@Composable
fun PermissionItem(
    permission: PermissionHelper.PermissionInfo,
    onRequestPermission: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = permission.name,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = permission.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 权限状态指示器
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (permission.isGranted) Icons.Default.Check else Icons.Default.Warning,
                        contentDescription = null,
                        tint = if (permission.isGranted)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (permission.isGranted) "已授予" else "未授予",
                        style = MaterialTheme.typography.labelSmall,
                        color = if (permission.isGranted)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.error
                    )
                }
            }
            
            // 如果权限未授予且是必需的，显示请求按钮
            if (!permission.isGranted && permission.isRequired) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onRequestPermission,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("请求此权限")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PermissionScreenPreview() {
    SynapseTheme {
        PermissionScreen()
    }
}
