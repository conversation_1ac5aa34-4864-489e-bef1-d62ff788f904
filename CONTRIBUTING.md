# 贡献指南

感谢您对Synapse项目的关注！我们欢迎各种形式的贡献。

## 🤝 如何贡献

### 报告问题
- 使用 [GitHub Issues](https://github.com/your-username/synapse/issues) 报告bug
- 提供详细的复现步骤
- 包含设备信息和Android版本
- 附上相关的日志信息

### 建议功能
- 在 [GitHub Discussions](https://github.com/your-username/synapse/discussions) 中讨论新功能
- 详细描述功能需求和使用场景
- 考虑功能的可行性和用户价值

### 提交代码
1. Fork项目到您的GitHub账号
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📝 开发规范

### 代码风格
- 使用Kotlin官方代码风格
- 遵循Android开发最佳实践
- 添加适当的注释和文档

### 提交信息
使用清晰的提交信息格式：
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

类型包括：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建或工具相关

### 测试
- 确保新功能有适当的测试
- 运行现有测试确保没有破坏
- 在不同设备上测试兼容性

## 🏗️ 开发环境

### 要求
- Android Studio Arctic Fox或更新版本
- JDK 11或更高版本
- Android SDK API 24+
- Git

### 设置
```bash
# 克隆项目
git clone https://github.com/your-username/synapse.git
cd synapse

# 在Android Studio中打开项目
# 同步Gradle依赖
# 连接Android设备或启动模拟器
```

### 构建
```bash
# Debug版本
./gradlew assembleDebug

# Release版本
./gradlew assembleRelease

# 运行测试
./gradlew test
```

## 📋 项目结构

```
app/src/main/java/com/ym/synapse/
├── api/                    # API接口和数据模型
├── config/                 # 配置相关界面
├── data/                   # 数据管理和存储
├── hook/                   # 系统Hook和后台服务
├── service/                # 业务逻辑服务
├── ui/                     # UI组件和主题
├── utils/                  # 工具类和帮助函数
└── MainActivity.kt         # 主Activity
```

## 🎯 贡献重点

### 优先级高的改进
1. **性能优化**: 减少内存使用，提升响应速度
2. **用户体验**: 改进界面交互和动画效果
3. **稳定性**: 修复崩溃和异常处理
4. **兼容性**: 支持更多设备和Android版本

### 欢迎的功能
1. **智能搜索**: 历史记录的全文搜索
2. **数据导出**: 支持更多导出格式
3. **主题定制**: 更多个性化选项
4. **多语言**: 国际化支持

### 技术债务
1. **代码重构**: 提升代码质量和可维护性
2. **测试覆盖**: 增加单元测试和集成测试
3. **文档完善**: 改进代码注释和API文档
4. **架构优化**: 改进模块间的依赖关系

## 🔍 代码审查

### 审查标准
- 代码质量和可读性
- 性能影响
- 安全性考虑
- 用户体验影响
- 测试覆盖率

### 审查流程
1. 自动化检查（CI/CD）
2. 代码风格检查
3. 功能测试
4. 人工代码审查
5. 合并到主分支

## 📚 学习资源

### Android开发
- [Android开发者官网](https://developer.android.com/)
- [Jetpack Compose指南](https://developer.android.com/jetpack/compose)
- [Kotlin官方文档](https://kotlinlang.org/docs/)

### 项目相关
- [Notion API文档](https://developers.notion.com/)
- [OpenAI API文档](https://platform.openai.com/docs/)
- [Retrofit使用指南](https://square.github.io/retrofit/)

## 🆘 获取帮助

### 联系方式
- GitHub Issues: 技术问题和bug报告
- GitHub Discussions: 功能讨论和一般问题
- 邮件: <EMAIL>

### 响应时间
- Issues: 通常在24-48小时内响应
- Pull Requests: 通常在1周内审查
- 紧急问题: 请在Issue中标注"urgent"

## 🎉 贡献者

感谢所有为项目做出贡献的开发者！

<!-- 这里会自动生成贡献者列表 -->

## 📄 许可证

通过贡献代码，您同意您的贡献将在MIT许可证下发布。

---

再次感谢您的贡献！每一个贡献都让Synapse变得更好。
