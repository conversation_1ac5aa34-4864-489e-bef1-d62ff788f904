# Synapse - 智能截图分析助手

<div align="center">

![Synapse Logo](https://img.shields.io/badge/Synapse-智能截图分析-blue?style=for-the-badge&logo=android)

[![Android](https://img.shields.io/badge/Android-7.0+-green?style=flat-square&logo=android)](https://developer.android.com)
[![Kotlin](https://img.shields.io/badge/Kotlin-1.9+-purple?style=flat-square&logo=kotlin)](https://kotlinlang.org)
[![Jetpack Compose](https://img.shields.io/badge/Jetpack%20Compose-1.5+-blue?style=flat-square&logo=jetpackcompose)](https://developer.android.com/jetpack/compose)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=flat-square)](LICENSE)

**一款基于AI的智能截图分析应用，让您的截图变成结构化知识**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [使用指南](#-使用指南) • [配置说明](#-配置说明) • [技术架构](#-技术架构)

</div>

## 📱 应用简介

Synapse是一款革命性的Android应用，通过AI技术将您的截图转化为结构化的知识内容。只需双击电源键截图，AI就会自动分析图片内容，提取关键信息，生成摘要和标签，并可自动同步到Notion知识库。

### 🎯 核心价值

- **🤖 AI驱动**: 支持OpenAI、Claude等主流AI模型
- **📚 知识管理**: 自动构建个人知识库
- **🏷️ 智能标签**: 自动生成和管理标签系统
- **🖼️ 图片展示**: 支持图床服务，在Notion中显示原图
- **⚡ 一键操作**: 双击电源键即可完成整个流程

## ✨ 功能特性

### 🔥 核心功能

#### 智能图片分析
- **三种分析模式**：
  - 📝 **Text-Heavy**: 文字密集型内容（文档、文章、代码等）
  - 🎨 **Rich-Content**: 富内容型（界面截图、图文混合等）
  - 🖼️ **Simple-Image**: 简单图片型（照片、图表等）
- **自动类型识别**: AI智能判断截图类型
- **精准内容提取**: 准确识别文字、表格、代码等

#### 智能标签管理
- **自动标签生成**: AI根据内容生成相关标签
- **标签去重合并**: 智能识别相似标签并自动合并
- **使用频率统计**: 跟踪标签使用情况
- **分类管理**: 按图片类型分类显示标签

#### Notion集成
- **自动同步**: 分析完成后自动发送到Notion数据库
- **结构化存储**: 标题、摘要、标签、类型、时间等完整信息
- **图片显示**: 支持图床服务，在Notion中展示原始截图
- **批量操作**: 支持历史记录的批量同步

### 🛠️ 高级功能

#### 图床服务
- **ImgBB**: 免费稳定，每月1000次上传
- **SM.MS**: 完全免费，支持游客模式
- **自动上传**: 截图自动上传并获取链接

#### 模型配置
- **灵活配置**: 支持共用模式和分离模式
- **多API支持**: OpenAI、Claude、本地部署等
- **自定义Prompt**: 为每种类型定制专属提示词

#### 用户体验
- **优雅动画**: 简约而精致的交互动效
- **分类设置**: 功能模块化的设置界面
- **历史管理**: 完整的分析历史记录
- **智能搜索**: 快速查找历史内容

## 🚀 快速开始

### 系统要求

- Android 7.0 (API 24) 或更高版本
- 至少 100MB 可用存储空间
- 网络连接（用于AI分析和同步）

### 安装步骤

1. **下载APK**
   ```bash
   # 克隆项目
   git clone https://github.com/your-username/synapse.git
   cd synapse
   
   # 构建APK
   ./gradlew assembleRelease
   ```

2. **安装应用**
   ```bash
   adb install app/build/outputs/apk/release/app-release.apk
   ```

3. **授予权限**
   - 通知权限
   - 存储权限
   - 相册读取权限
   - 自启动权限（可选）
   - 后台运行权限（可选）

### 基础配置

1. **API配置**
   - 设置AI服务的API地址和密钥
   - 选择合适的模型ID

2. **Notion集成**（可选）
   - 创建Notion Integration
   - 配置数据库ID
   - 启用自动同步

3. **图床服务**（可选）
   - 选择图床服务提供商
   - 配置API密钥

## 📖 使用指南

### 基本使用流程

1. **截图触发**
   ```
   双击电源键 → 系统截图 → Synapse自动检测
   ```

2. **AI分析**
   ```
   图片类型识别 → 内容分析 → 生成摘要和标签
   ```

3. **结果展示**
   ```
   显示分析结果 → 可编辑修改 → 保存到历史
   ```

4. **同步到Notion**（如果启用）
   ```
   自动上传图片 → 创建Notion页面 → 包含完整信息
   ```

### 高级使用技巧

#### 自定义Prompt
```
# 在模型配置中可以自定义提示词
# 支持变量替换：{TAG_SUGGESTIONS}
请分析这张图片，重点关注：
1. 技术内容的准确性
2. 代码的语言和功能
3. 生成相关的技术标签
{TAG_SUGGESTIONS}
```

#### 标签管理
- 定期查看标签统计
- 清理低频使用的标签
- 观察标签使用模式

#### 批量操作
- 选择多个历史记录
- 批量发送到Notion
- 批量删除或导出

## ⚙️ 配置说明

### API配置

#### OpenAI配置
```json
{
  "api_url": "https://api.openai.com/v1/chat/completions",
  "api_key": "sk-your-api-key",
  "model_id": "gpt-4-vision-preview"
}
```

#### Claude配置
```json
{
  "api_url": "https://api.anthropic.com/v1/messages",
  "api_key": "sk-ant-your-api-key",
  "model_id": "claude-3-sonnet-20240229"
}
```

### Notion数据库结构

创建数据库时需要包含以下属性：

| 属性名 | 类型 | 说明 |
|--------|------|------|
| 标题 | Title | 页面标题 |
| 摘要 | Text | 内容摘要 |
| 标签 | Multi-select | 分类标签 |
| 类型 | Select | 图片类型 |
| 创建时间 | Created time | 创建时间 |

### 图床服务配置

#### ImgBB
1. 访问 [ImgBB API](https://api.imgbb.com/)
2. 注册账号并获取API Key
3. 在应用中配置API Key

#### SM.MS
1. 访问 [SM.MS](https://sm.ms/)
2. 注册账号（可选，支持游客模式）
3. 获取API Token并配置

## 🏗️ 技术架构

### 技术栈

- **UI框架**: Jetpack Compose
- **编程语言**: Kotlin
- **架构模式**: MVVM + Repository
- **网络请求**: Retrofit + OkHttp
- **数据存储**: SharedPreferences + JSON
- **图片处理**: Android Bitmap API
- **动画系统**: Compose Animation

### 项目结构

```
app/src/main/java/com/ym/synapse/
├── api/                    # API接口定义
├── config/                 # 配置界面
├── data/                   # 数据模型和管理
├── hook/                   # 系统Hook和服务
├── service/                # 业务服务层
├── ui/                     # UI组件和主题
├── utils/                  # 工具类
└── MainActivity.kt         # 主Activity
```

### 核心模块

#### AI分析模块
- **AiOcrHelper**: AI分析核心逻辑
- **ModelConfigManager**: 模型配置管理
- **NotionAnalysisResult**: 分析结果数据结构

#### 标签管理模块
- **TagManager**: 智能标签管理
- **相似度算法**: 基于编辑距离的标签匹配
- **使用统计**: 标签频率和关联分析

#### 集成服务模块
- **NotionService**: Notion API集成
- **ImageHostService**: 图床服务集成
- **权限管理**: 系统权限检查和请求

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📞 支持与反馈

- **问题报告**: [GitHub Issues](https://github.com/your-username/synapse/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-username/synapse/discussions)
- **邮件联系**: <EMAIL>

## 🙏 致谢

感谢以下开源项目和服务：

- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Retrofit](https://square.github.io/retrofit/)
- [Notion API](https://developers.notion.com/)
- [ImgBB](https://imgbb.com/) & [SM.MS](https://sm.ms/)

---

<div align="center">

**如果这个项目对您有帮助，请给个 ⭐ Star！**

Made with ❤️ by [Your Name]

</div>
