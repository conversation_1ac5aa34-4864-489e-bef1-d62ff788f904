# 智能标签管理系统

## 概述

Synapse现在具备了智能标签管理功能，可以自动维护标签的一致性，避免标签过多和重复的问题。

## 核心功能

### 1. 智能标签匹配
- **相似度检测**: 使用编辑距离算法检测相似标签
- **自动合并**: 相似度超过70%的标签会自动使用已有标签
- **优先级排序**: 按使用频率和最近使用时间排序

### 2. 标签建议系统
- **类型特定建议**: 为每种图片类型提供常用标签建议
- **动态Prompt**: AI会收到当前可用标签列表，优先使用已有标签
- **上下文感知**: 根据图片类型和历史使用情况提供智能建议

### 3. 标签统计和管理
- **使用频率统计**: 记录每个标签的使用次数
- **类型关联**: 跟踪标签与图片类型的关联关系
- **自动清理**: 支持清理低频使用的标签

## 工作流程

### 标签处理流程
```
AI生成标签 → 相似度检测 → 选择已有/创建新标签 → 更新统计 → 发送到Notion
```

### 相似度匹配示例
- "技术文档" vs "技术文档" → 100%匹配，使用已有标签
- "技术文档" vs "技术资料" → 80%相似，使用已有标签"技术文档"
- "技术文档" vs "美食记录" → 20%相似，创建新标签

## 使用方法

### 自动化使用
1. **正常截图分析**: 系统会自动处理标签
2. **智能合并**: 相似标签会自动合并到已有标签
3. **Notion同步**: 处理后的标签会发送到Notion

### 手动管理
1. **查看标签统计**: 在设置中点击"管理标签"
2. **清理低频标签**: 删除使用次数少的标签
3. **查看类型分布**: 了解不同图片类型的常用标签

## 标签建议机制

### AI Prompt增强
系统会在AI分析时提供以下信息：
- 当前图片类型的常用标签（前15个）
- 所有可用标签（前30个）
- 优先使用已有标签的指导

### 示例Prompt片段
```
# 标签选择指导
常用的Text-Heavy类型标签：技术文档、会议纪要、学习笔记、代码片段、操作指南
所有可用标签：技术文档、会议纪要、学习笔记、代码片段、操作指南、美食记录、旅行回忆...
请优先从上述标签中选择合适的，如果没有合适的再创建新标签。
```

## 配置选项

### 相似度阈值
- **默认值**: 70%
- **调整建议**: 
  - 提高阈值(80-90%): 更严格的匹配，减少误合并
  - 降低阈值(50-60%): 更宽松的匹配，更多标签合并

### 清理策略
- **低频标签**: 使用次数少于2次的标签
- **清理时机**: 手动触发或定期清理
- **备份机制**: 清理前会显示确认对话框

## 最佳实践

### 标签命名规范
1. **简洁明确**: 使用2-4个字的标签
2. **避免重复**: 不要创建意思相同的标签
3. **分类清晰**: 按功能、类型、主题分类

### 维护建议
1. **定期检查**: 每月查看一次标签统计
2. **清理冗余**: 及时清理低频和重复标签
3. **标准化**: 统一相似标签的命名

### 使用技巧
1. **让AI学习**: 多次使用后，AI会更好地选择标签
2. **手动调整**: 在Notion中可以手动调整标签
3. **反馈循环**: 系统会从使用模式中学习

## 技术实现

### 相似度算法
- **编辑距离**: 计算字符串的最小编辑操作数
- **包含关系**: 检测一个标签是否包含另一个
- **完全匹配**: 优先级最高的匹配方式

### 数据存储
- **本地存储**: 使用SharedPreferences存储标签数据
- **JSON格式**: 标签数据以JSON格式序列化
- **实时同步**: 每次使用后立即更新统计

### 性能优化
- **缓存机制**: 标签数据会被缓存以提高性能
- **异步处理**: 标签匹配在后台线程进行
- **增量更新**: 只更新变化的标签数据

## 故障排除

### 标签不匹配
- **检查相似度**: 确认标签确实相似
- **调整阈值**: 可能需要调整相似度阈值
- **手动合并**: 在标签管理界面手动处理

### 性能问题
- **清理数据**: 定期清理低频标签
- **重置缓存**: 重启应用刷新缓存
- **数据备份**: 重要标签数据建议备份

### 同步问题
- **检查网络**: 确保网络连接正常
- **验证配置**: 检查Notion配置是否正确
- **查看日志**: 在Logcat中查看详细错误信息

## 未来改进

### 计划功能
1. **标签推荐**: 基于内容智能推荐标签
2. **批量管理**: 支持批量编辑和合并标签
3. **导入导出**: 支持标签数据的导入导出
4. **云端同步**: 跨设备同步标签数据

### 算法优化
1. **机器学习**: 使用ML算法改进标签匹配
2. **语义分析**: 基于语义而非字符匹配标签
3. **用户反馈**: 根据用户反馈调整匹配策略

这个智能标签管理系统将大大提高Notion知识库的组织效率，确保标签的一致性和可维护性。
