# 图床服务设置指南

## 概述

为了在Notion中显示截图，需要将图片上传到图床服务。本应用支持两种免费图床服务：

## 1. ImgBB (推荐)

### 优点
- ✅ 免费且稳定
- ✅ 支持大文件上传
- ✅ 提供删除链接
- ✅ 国际访问速度好

### 限制
- 每月1000次上传限制
- 需要注册账号

### 设置步骤

1. **注册账号**
   - 访问 [ImgBB](https://imgbb.com/)
   - 点击右上角"Sign up"注册账号

2. **获取API Key**
   - 登录后访问 [API页面](https://api.imgbb.com/)
   - 复制你的API Key

3. **在应用中配置**
   - 打开Synapse设置
   - 启用"图床服务"
   - 选择"ImgBB"
   - 输入API Key
   - 点击"测试图床连接"

## 2. SM.MS

### 优点
- ✅ 完全免费
- ✅ 无上传次数限制
- ✅ 支持游客模式（无需API Key）
- ✅ 国内访问速度较好

### 限制
- 单文件最大5MB
- 游客模式有速率限制

### 设置步骤

#### 游客模式（推荐新手）
1. 在应用中选择"SM.MS"
2. API Key留空
3. 点击"测试图床连接"

#### 注册模式（推荐长期使用）
1. **注册账号**
   - 访问 [SM.MS](https://sm.ms/)
   - 注册账号

2. **获取API Key**
   - 登录后访问 [API Token页面](https://sm.ms/home/<USER>
   - 生成新的API Token

3. **在应用中配置**
   - 选择"SM.MS"
   - 输入API Key
   - 点击"测试图床连接"

## 使用方法

### 自动上传
1. 启用图床服务
2. 启用Notion自动同步
3. 截图后会自动上传图片并发送到Notion

### 手动上传
- 从历史记录或详情页面手动发送到Notion时会自动上传图片

## 故障排除

### ImgBB问题

#### 401 Unauthorized
- **原因**: API Key不正确
- **解决**: 重新复制API Key，确保没有多余空格

#### 400 Bad Request
- **原因**: 图片格式不支持或文件损坏
- **解决**: 检查截图文件是否正常

#### 429 Too Many Requests
- **原因**: 超过每月1000次限制
- **解决**: 等待下个月或考虑使用SM.MS

### SM.MS问题

#### 413 Request Entity Too Large
- **原因**: 文件超过5MB限制
- **解决**: 考虑使用ImgBB或压缩图片

#### 429 Too Many Requests
- **原因**: 游客模式速率限制
- **解决**: 注册账号获取API Key

### 通用问题

#### 网络连接失败
- 检查网络连接
- 尝试切换网络（WiFi/移动数据）
- 确认可以访问图床网站

#### 上传超时
- 检查图片文件大小
- 尝试在网络较好的环境下使用

## 隐私说明

- 图片会上传到第三方图床服务
- 请确保截图不包含敏感信息
- 建议定期清理不需要的图片

## 推荐配置

### 个人使用
- **推荐**: ImgBB
- **原因**: 稳定性好，支持大文件

### 团队使用
- **推荐**: SM.MS (注册模式)
- **原因**: 无上传次数限制

### 临时测试
- **推荐**: SM.MS (游客模式)
- **原因**: 无需注册，快速开始

## 注意事项

1. **备份重要图片**: 图床服务可能会删除长期未访问的图片
2. **遵守服务条款**: 不要上传违法或侵权内容
3. **监控使用量**: 注意API调用次数限制
4. **网络环境**: 某些网络环境可能无法访问特定图床服务
