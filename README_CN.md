# Synapse - 智能截图分析助手

> 🤖 让AI帮你把截图变成结构化知识

## 🎯 这是什么？

Synapse是一个Android应用，可以：
- 双击电源键截图
- AI自动分析图片内容
- 生成标题、摘要、标签
- 自动发送到Notion知识库
- 支持图片在Notion中显示

## ✨ 主要功能

### 📱 一键截图分析
```
双击电源键 → 截图 → AI分析 → 生成摘要 → 保存到Notion
```

### 🧠 三种分析模式
- **文字密集型**: 文档、文章、代码
- **富内容型**: 界面截图、图文混合
- **简单图片型**: 照片、图表

### 🏷️ 智能标签管理
- 自动生成标签
- 相似标签合并
- 使用频率统计
- 保持标签一致性

### 📚 Notion集成
- 自动创建页面
- 结构化存储信息
- 支持图片显示
- 批量同步历史

## 🚀 快速开始

### 1. 安装应用
```bash
# 构建APK
./gradlew assembleRelease

# 安装到手机
adb install app/build/outputs/apk/release/app-release.apk
```

### 2. 配置权限
打开应用后按提示授予：
- 通知权限
- 存储权限
- 相册读取权限

### 3. 配置API
在设置中配置：
- API地址（如：OpenAI、Claude）
- API密钥
- 模型ID

### 4. 配置Notion（可选）
- 创建Notion Integration
- 设置数据库ID
- 启用自动同步

## 📖 使用方法

### 基础使用
1. 双击电源键截图
2. 等待AI分析完成
3. 查看生成的摘要和标签
4. 自动保存到历史记录

### 高级功能
- **手动发送**: 从历史记录手动发送到Notion
- **编辑结果**: 修改AI生成的内容
- **标签管理**: 查看和清理标签
- **批量操作**: 选择多个记录批量处理

## ⚙️ 配置说明

### API配置示例

#### OpenAI
```
API地址: https://api.openai.com/v1/chat/completions
API密钥: sk-your-openai-key
模型ID: gpt-4-vision-preview
```

#### Claude
```
API地址: https://api.anthropic.com/v1/messages
API密钥: sk-ant-your-claude-key
模型ID: claude-3-sonnet-20240229
```

### Notion数据库设置

创建数据库时添加这些属性：
- **标题** (Title)
- **摘要** (Text)
- **标签** (Multi-select)
- **类型** (Select): Text-Heavy, Rich-Content, Simple-Image
- **创建时间** (Created time)

### 图床服务（可选）

#### ImgBB（推荐）
- 免费稳定
- 每月1000次上传
- 需要API Key

#### SM.MS
- 完全免费
- 支持游客模式
- 单文件5MB限制

## 🛠️ 技术特性

### 核心技术
- **Jetpack Compose**: 现代化UI
- **Kotlin**: 100%Kotlin开发
- **AI集成**: 支持多种AI模型
- **智能算法**: 标签相似度匹配

### 性能优化
- 异步处理
- 内存优化
- 电池友好
- 流畅动画

## 📁 项目结构

```
synapse/
├── app/src/main/java/com/ym/synapse/
│   ├── api/           # API接口
│   ├── data/          # 数据管理
│   ├── service/       # 业务服务
│   ├── ui/            # 界面组件
│   └── utils/         # 工具类
├── docs/              # 文档
└── README.md          # 说明文档
```

## 🔧 开发指南

### 构建项目
```bash
git clone https://github.com/your-username/synapse.git
cd synapse
./gradlew assembleDebug
```

### 调试模式
设置环境变量启用详细日志：
```bash
export MCP_DEBUG=true
```

## 📋 TODO

- [ ] 智能搜索功能
- [ ] 云端数据同步
- [ ] 更多AI模型支持
- [ ] 插件系统
- [ ] 多语言支持

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢以下项目和服务：
- Jetpack Compose
- Notion API
- OpenAI & Anthropic
- ImgBB & SM.MS

---

**如果觉得有用，请给个⭐Star！**
